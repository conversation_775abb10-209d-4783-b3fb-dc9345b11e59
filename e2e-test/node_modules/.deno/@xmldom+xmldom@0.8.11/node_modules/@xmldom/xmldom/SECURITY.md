# Security Policy

The most up-to-date version of this document can be found at <https://github.com/xmldom/xmldom/security/policy>.

## Supported Versions

This repository contains the code for the libraries `xmldom` and `@xmldom/xmldom` on npm.

As long as we didn't publish v1, we aim to maintain the last two minor versions with security fixes. If it is possible we provide security fixes as path versions.
If you think there is a good reason to also patch an earlier version let us know in a github issue or the release discussion once the fix has been provided. 
The maintainers will consider it and if we agree and have/find the required resources, a patch for that version will be provided.

Please notice that [we are no longer able to publish the (unscoped) `xmldom` package](https://github.com/xmldom/xmldom/issues/271), 
and that all existing versions of `xmldom` are affected by at least one security vulnerability and should be considered deprecated.
You can still report issues regarding `xmldom` as described below.

If you need help with migrating from `xmldom` to `@xmldom/xmldom`, file a github issue or PR in the affected repository and mention @karfau.

## Reporting vulnerabilities

Please email reports about any security related issues you find to `<EMAIL>`, which will forward it to the list of maintainers. 
The maintainers will try to respond within 7 calendar days. (If nobody peplies after 7 days, please us send a reminder!)
As part of you communication please make sure to always hit "Reply all", so all maintainers are kept in the loop.

In addition, please include the following information along with your report:

- Your name and affiliation (if any).
- A description of the technical details of the vulnerabilities. It is very important to let us know how we can reproduce your findings.
- An explanation who can exploit this vulnerability, and what they gain when doing so -- write an attack scenario. This will help us evaluate your report quickly, especially if the issue is complex.
- Whether this vulnerability public or known to third parties. If it is, please provide details.

If you believe that an existing (public) issue is security-related, please send an email to `<EMAIL>`. 
The email should include the issue URL and a short description of why it should be handled according to this security policy.

Once an issue is reported, the maintainers use the following disclosure process:

- When a report is received, we confirm the issue, determine its severity and the affected versions.
- If we know of specific third-party services or software based on xmldom that require mitigation before publication, those projects will be notified.
- A [github security advisory](https://docs.github.com/en/code-security/security-advisories/about-github-security-advisories) is [created](https://docs.github.com/en/code-security/security-advisories/creating-a-security-advisory) (but not published) which details the problem and steps for mitigation.
- If the reporter provides a github account and agrees to it, we (add that github account as a collaborator on the advisuory)[https://docs.github.com/en/code-security/security-advisories/adding-a-collaborator-to-a-security-advisory].
- The vulnerability is fixed in a [private fork](https://docs.github.com/en/code-security/security-advisories/collaborating-in-a-temporary-private-fork-to-resolve-a-security-vulnerability) and potential workarounds are identified.
- The maintainers audit the existing code to find any potential similar problems.
- The release for the current minor version and the [security advisory are published](https://docs.github.com/en/code-security/security-advisories/publishing-a-security-advisory).
- The release(s) for previous minor version(s) are published.

We credit reporters for identifying security issues, if they confirm that they want to.

## Known vulnerabilities

See https://github.com/xmldom/xmldom/security/advisories?state=published
