{"version": 3, "file": "StringWalker.js", "sourceRoot": "", "sources": ["../src/StringWalker.ts"], "names": [], "mappings": ";;AAAA;;GAEG;AACH;IAUE;;;;OAIG;IACH,sBAAY,KAAa;QAXjB,aAAQ,GAAW,CAAC,CAAA;QAY1B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA;IACnC,CAAC;IAKD,sBAAI,6BAAG;QAHP;;WAEG;aACH,cAAqB,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAA,CAAC,CAAC;;;OAAA;IAK3D,sBAAI,gCAAM;QAHV;;WAEG;aACH,cAAuB,OAAO,IAAI,CAAC,OAAO,CAAA,CAAC,CAAC;;;OAAA;IAE5C;;;OAGG;IACH,gCAAS,GAAT;QACE,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;YACjC,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;aACrB;iBAAM;gBACL,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;gBACpD,0BAA0B;gBAC1B,IAAI,EAAE,KAAK,SAAS,EAAE;oBACpB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;iBACrB;qBAAM;oBACL,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAA;iBACrB;aACF;SACF;QACD,OAAO,IAAI,CAAC,UAAU,CAAA;IACxB,CAAC;IAED;;;OAGG;IACH,wBAAC,GAAD;QACE,IAAI,IAAI,CAAC,EAAE,KAAK,SAAS,EAAE;YACzB,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAA;SACvD;QACD,OAAO,IAAI,CAAC,EAAE,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,gCAAS,GAAT;QACE,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;YACjC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC3B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;SACtD;QACD,OAAO,IAAI,CAAC,UAAU,CAAA;IACxB,CAAC;IAED;;OAEG;IACH,gCAAS,GAAT;QACE,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;YACjC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC3B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;SAClD;QACD,OAAO,IAAI,CAAC,UAAU,CAAA;IACxB,CAAC;IAKD,sBAAI,iCAAO;QAHX;;WAEG;aACH,cAAyB,OAAO,IAAI,CAAC,QAAQ,CAAA,CAAC,CAAC;aAC/C,UAAY,GAAW;YACrB,IAAI,GAAG,KAAK,IAAI,CAAC,QAAQ;gBAAE,OAAM;YAEjC,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAA;YAEnB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;YAC3B,IAAI,CAAC,EAAE,GAAG,SAAS,CAAA;YACnB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;YAC3B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;QAC7B,CAAC;;;OAV8C;IAWjD,mBAAC;AAAD,CAAC,AAlGD,IAkGC;AAlGY,oCAAY"}