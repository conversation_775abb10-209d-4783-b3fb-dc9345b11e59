{"version": 3, "file": "String.js", "sourceRoot": "", "sources": ["../src/String.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAA+C;AAC/C,+CAAiD;AACjD,+BAAoC;AACpC,uCAAwC;AAExC;;;;;GAKG;AACH,SAAgB,gBAAgB,CAAC,CAAS,EAAE,CAAS;IACnD;;;;;;;;;;OAUG;IACH,IAAI,CAAC,GAAG,CAAC,CAAA;IACT,OAAO,IAAI,EAAE;QACX,IAAM,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;QACvD,IAAM,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;QACvD,IAAI,SAAS,KAAK,IAAI;YAAE,OAAO,IAAI,CAAA;QACnC,IAAI,SAAS,KAAK,SAAS;YAAE,OAAO,KAAK,CAAA;QACzC,CAAC,EAAE,CAAA;KACJ;AACH,CAAC;AApBD,4CAoBC;AAED;;;;;GAKG;AACH,SAAgB,kBAAkB,CAAC,CAAS,EAAE,CAAS;IACrD;;;;;;;;;OASG;IACH,IAAI,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;QAAE,OAAO,KAAK,CAAA;IACxC,IAAI,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;QAAE,OAAO,IAAI,CAAA;IACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE;QACrD,IAAM,SAAS,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QACjC,IAAM,SAAS,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAA;QACjC,IAAI,SAAS,KAAK,SAAS;YAAE,SAAQ;QACrC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC,CAAA;KAC/B;IACD,0BAA0B;IAC1B,OAAO,KAAK,CAAA;AACd,CAAC;AArBD,gDAqBC;AAED;;;;GAIG;AACH,SAAgB,gBAAgB,CAAC,GAAW;;IAC1C;;;;OAIG;IACH,IAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAClC,IAAM,KAAK,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;IAC/C,IAAI,CAAC,GAAG,CAAC,CAAA;;QACT,KAAwB,IAAA,QAAA,SAAA,GAAG,CAAA,wBAAA,yCAAE;YAAxB,IAAM,SAAS,gBAAA;YAClB,IAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;YACrC,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,IAAI,MAAM,EAAE,4EAA4E,CAAC,CAAA;YAClI,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,IAAI,MAAM,EAAE;gBACxC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAA;aAClB;SACF;;;;;;;;;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAjBD,4CAiBC;AAED;;;;GAIG;AACH,SAAgB,aAAa,CAAC,GAAW;IACvC;;OAEG;IACH,OAAO,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACvC,CAAC;AALD,sCAKC;AAED;;;;GAIG;AACH,SAAgB,cAAc,CAAC,GAAW;;IACxC;;;OAGG;IACH,IAAI,MAAM,GAAG,EAAE,CAAA;;QACf,KAAgB,IAAA,QAAA,SAAA,GAAG,CAAA,wBAAA,yCAAE;YAAhB,IAAM,CAAC,gBAAA;YACV,IAAM,IAAI,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;YAC7B,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;gBACtD,MAAM,IAAI,MAAM,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,CAAA;aAC5C;iBAAM;gBACL,MAAM,IAAI,CAAC,CAAA;aACZ;SACF;;;;;;;;;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAfD,wCAeC;AAED;;;;GAIG;AACH,SAAgB,cAAc,CAAC,GAAW;;IACxC;;;OAGG;IACH,IAAI,MAAM,GAAG,EAAE,CAAA;;QACf,KAAgB,IAAA,QAAA,SAAA,GAAG,CAAA,wBAAA,yCAAE;YAAhB,IAAM,CAAC,gBAAA;YACV,IAAM,IAAI,GAAG,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;YAC7B,IAAI,IAAI,KAAK,SAAS,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;gBACtD,MAAM,IAAI,MAAM,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,CAAA;aAC5C;iBAAM;gBACL,MAAM,IAAI,CAAC,CAAA;aACZ;SACF;;;;;;;;;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAfD,wCAeC;AAED;;;;;GAKG;AACH,SAAgB,yBAAyB,CAAC,CAAS,EAAE,CAAS;IAC5D;;;OAGG;IACH,OAAO,cAAc,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC,CAAC,CAAC,CAAA;AAChD,CAAC;AAND,8DAMC;AAED;;;;GAIG;AACH,SAAgB,WAAW,CAAC,GAAW;IACrC;;;OAGG;IACH,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE,uCAAuC,CAAC,CAAA;IAE3E,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAA;AAC9B,CAAC;AARD,kCAQC;AAED;;;;GAIG;AACH,SAAgB,WAAW,CAAC,KAAiB;;;QAC3C;;;WAGG;QACH,KAAmB,IAAA,UAAA,SAAA,KAAK,CAAA,4BAAA,+CAAE;YAArB,IAAM,IAAI,kBAAA;YACb,OAAO,CAAC,MAAM,CAAC,kBAAW,CAAC,IAAI,CAAC,EAAE,8CAA8C,CAAC,CAAA;SAClF;;;;;;;;;IAED,OAAO,+BAAgB,CAAC,KAAK,CAAC,CAAA;AAChC,CAAC;AAVD,kCAUC;AAED;;;;GAIG;AACH,SAAgB,aAAa,CAAC,GAAW;IACvC;;;OAGG;IACH,OAAO,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA;AACnC,CAAC;AAND,sCAMC;AAED;;;;;;GAMG;AACH,SAAgB,iBAAiB,CAAC,GAAW;IAC3C;;;;OAIG;IACH,OAAO,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;AACxD,CAAC;AAPD,8CAOC;AAED;;;;GAIG;AACH,SAAgB,sCAAsC,CAAC,GAAW;IAChE;;;OAGG;IACH,OAAO,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAA;AACtE,CAAC;AAND,wFAMC;AAED;;;;GAIG;AACH,SAAgB,+BAA+B,CAAC,GAAW;IACzD;;;;;OAKG;IACH,OAAO,sCAAsC,CAAC,GAAG,CAAC,OAAO,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC,CAAA;AACrF,CAAC;AARD,0EAQC;AAED;;;;;;;GAOG;AACH,SAAgB,4BAA4B,CAAC,SAAqC,EAChF,KAAwB,EAAE,OAA6B;IACvD;;;;;;;OAOG;IACH,IAAI,CAAC,cAAO,CAAC,KAAK,CAAC;QAAE,OAAO,4BAA4B,CAAC,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAA;IAE/F,IAAI,MAAM,GAAG,EAAE,CAAA;IACf,OAAO,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE;QACzF,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QACjC,OAAO,CAAC,QAAQ,EAAE,CAAA;KACnB;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAlBD,oEAkBC;AAED;;;;;GAKG;AACH,SAAgB,mBAAmB,CAAC,KAAwB,EAAE,OAA6B;IACzF;;;;;OAKG;IACH,4BAA4B,CAAC,UAAA,GAAG,IAAI,OAAA,4BAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAzB,CAAyB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;AAChF,CAAC;AARD,kDAQC;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,KAAwB,EAAE,SAAiB;IACvE;;;;;;;;;;;;;;OAcG;IACH,IAAI,CAAC,cAAO,CAAC,KAAK,CAAC;QAAE,OAAO,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC,CAAA;IAEvE,IAAM,OAAO,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAA;IAC/B,IAAM,MAAM,GAAa,EAAE,CAAA;IAC3B,IAAI,KAAK,GAAG,4BAA4B,CAAC,UAAA,GAAG,IAAI,OAAA,SAAS,KAAK,GAAG,EAAjB,CAAiB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;IAClF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAClB,OAAO,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE;QACtC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,SAAS,EAAE,mDAAmD,CAAC,CAAA;QAC1G,OAAO,CAAC,QAAQ,EAAE,CAAA;QAClB,KAAK,GAAG,4BAA4B,CAAC,UAAA,GAAG,IAAI,OAAA,SAAS,KAAK,GAAG,EAAjB,CAAiB,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;QAC9E,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;KACnB;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AA7BD,sCA6BC;AAED;;;;GAIG;AACH,SAAgB,6BAA6B,CAAC,KAAwB;IACpE;;;;;;;;;;;OAWG;IACH,IAAI,CAAC,cAAO,CAAC,KAAK,CAAC;QAAE,OAAO,6BAA6B,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;IAE5E,IAAM,OAAO,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAA;IAC/B,IAAM,MAAM,GAAa,EAAE,CAAA;IAC3B,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;IACnC,OAAO,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE;QACtC,IAAM,KAAK,GAAG,4BAA4B,CAAC,UAAA,GAAG,IAAI,OAAA,CAAC,4BAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAA1B,CAA0B,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;QAC7F,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAClB,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;KACpC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAxBD,sEAwBC;AAED;;;;GAIG;AACH,SAAgB,oBAAoB,CAAC,KAAwB;IAC3D;;;;;;;;;;;;;OAaG;IACH,IAAI,CAAC,cAAO,CAAC,KAAK,CAAC;QAAE,OAAO,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;IAEnE,IAAM,OAAO,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAA;IAC/B,IAAM,MAAM,GAAa,EAAE,CAAA;IAC3B,OAAO,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE;QACtC,IAAM,KAAK,GAAG,4BAA4B,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,KAAK,GAAG,EAAX,CAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;QAC9E,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,KAAK,CAAC,CAAC,CAAA;QAC1D,IAAI,OAAO,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,EAAE;YACnC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,GAAG,EAAE,0DAA0D,CAAC,CAAA;YAC3G,OAAO,CAAC,QAAQ,EAAE,CAAA;SACnB;KACF;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AA5BD,oDA4BC;AAED;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,IAAc,EAAE,SAAsB;IAAtB,0BAAA,EAAA,cAAsB;IAChE;;;;;OAKG;IACH,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;QAAE,OAAO,EAAE,CAAA;IAChC,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;AAC7B,CAAC;AATD,kCASC"}