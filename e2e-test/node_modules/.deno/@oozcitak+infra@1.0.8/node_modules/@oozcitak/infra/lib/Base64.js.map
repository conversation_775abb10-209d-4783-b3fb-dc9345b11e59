{"version": 3, "file": "Base64.js", "sourceRoot": "", "sources": ["../src/Base64.ts"], "names": [], "mappings": ";;AAAA,2CAA8C;AAE9C;;;;GAIG;AACH,SAAgB,qBAAqB,CAAC,KAAa;IACjD;;;;OAIG;IACH,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;AAC9C,CAAC;AAPD,sDAOC;AAED;;;;GAIG;AACH,SAAgB,qBAAqB,CAAC,KAAa;IACjD,IAAI,KAAK,KAAK,EAAE;QAAE,OAAO,EAAE,CAAA;IAC3B;;OAEG;IACH,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,4BAAe,EAAE,EAAE,CAAC,CAAA;IAC1C;;;OAGG;IACH,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;QAC1B,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAG;YACzB,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;SAC1C;aAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAG;YAC/B,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;SAC1C;KACF;IACD;;OAEG;IACH,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC;QAAE,OAAO,IAAI,CAAA;IACvC;;;;;;OAMG;IACH,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC;QAAE,OAAO,IAAI,CAAA;IAC7C;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;AACtD,CAAC;AArDD,sDAqDC"}