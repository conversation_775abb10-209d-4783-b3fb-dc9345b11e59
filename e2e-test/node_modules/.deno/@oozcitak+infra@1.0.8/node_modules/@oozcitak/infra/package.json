{"name": "@oozcitak/infra", "version": "1.0.8", "keywords": ["infra", "whatwg"], "homepage": "http://github.com/oozcitak/infra", "description": "An implementation of the Infra Living Standard", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": [], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/oozcitak/infra.git"}, "bugs": {"url": "http://github.com/oozcitak/infra/issues"}, "main": "./lib/index", "types": "lib/index.d.ts", "engines": {"node": ">=6.0"}, "files": ["lib/**/*"], "dependencies": {"@oozcitak/util": "8.3.8"}, "devDependencies": {"@types/jest": "^24.0.22", "@types/node": "^12.12.6", "coveralls": "^3.0.7", "jest": "^24.9.0", "ts-jest": "^24.1.0", "typescript": "^3.7.2"}, "jest": {"transform": {"^.+\\.tsx?$": "ts-jest"}, "testRegex": "/test/.*\\.test\\.tsx?$", "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.{ts,tsx}"]}, "scripts": {"compile": "rm -rf lib && tsc --version && tsc", "test": "npm run compile && jest --coverage", "publish-public": "npm run test && npm publish --access public"}}