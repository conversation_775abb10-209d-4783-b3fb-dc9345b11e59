{"version": 3, "file": "TreeWalkerAlgorithm.js", "sourceRoot": "", "sources": ["../../src/algorithm/TreeWalkerAlgorithm.ts"], "names": [], "mappings": ";;AAAA,gDAAkE;AAClE,2DAAuD;AAEvD;;;;;;GAMG;AACH,SAAgB,2BAA2B,CAAC,MAAkB,EAAE,KAAc;IAC5E;;;;;OAKG;IACH,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;IAC7E,OAAO,IAAI,KAAK,IAAI,EAAE;QACpB;;WAEG;QACH,IAAM,MAAM,GAAG,qCAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAE7C,IAAI,MAAM,KAAK,yBAAY,CAAC,MAAM,EAAE;YAClC;;;eAGG;YACH,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAA;YACtB,OAAO,IAAI,CAAA;SACZ;aAAM,IAAI,MAAM,KAAK,yBAAY,CAAC,IAAI,EAAE;YACvC;;;;;eAKG;YACH,IAAM,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YAC1D,IAAI,KAAK,KAAK,IAAI,EAAE;gBAClB,IAAI,GAAG,KAAK,CAAA;gBACZ,SAAQ;aACT;SACF;QAED;;WAEG;QACH,OAAO,IAAI,KAAK,IAAI,EAAE;YACpB;;;;eAIG;YACH,IAAM,OAAO,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;YACnE,IAAI,OAAO,KAAK,IAAI,EAAE;gBACpB,IAAI,GAAG,OAAO,CAAA;gBACd,MAAK;aACN;YAED;;;;eAIG;YACH,IAAM,MAAM,GAAgB,IAAI,CAAC,OAAO,CAAA;YACxC,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,MAAM,CAAC,KAAK,IAAI,MAAM,KAAK,MAAM,CAAC,QAAQ,EAAE;gBAC5E,OAAO,IAAI,CAAA;aACZ;YACD;;eAEG;YACH,IAAI,GAAG,MAAM,CAAA;SACd;KACF;IAED;;OAEG;IACH,OAAO,IAAI,CAAA;AACb,CAAC;AAtED,kEAsEC;AAED;;;;;;GAMG;AACH,SAAgB,2BAA2B,CAAC,MAAkB,EAAE,IAAa;IAC3E;;;;OAIG;IACH,IAAI,IAAI,GAAgB,MAAM,CAAC,QAAQ,CAAA;IACvC,IAAI,IAAI,KAAK,MAAM,CAAC,KAAK;QAAE,OAAO,IAAI,CAAA;IAEtC,OAAO,IAAI,EAAE;QACX;;;;WAIG;QACH,IAAI,OAAO,GAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QAE7E,OAAO,OAAO,KAAK,IAAI,EAAE;YACvB;;;;;eAKG;YACH,IAAI,GAAG,OAAO,CAAA;YACd,IAAM,MAAM,GAAG,qCAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;YAC7C,IAAI,MAAM,KAAK,yBAAY,CAAC,MAAM,EAAE;gBAClC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACtB,OAAO,IAAI,CAAA;aACZ;YAED;;;;;;eAMG;YACH,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YACrD,IAAI,MAAM,KAAK,yBAAY,CAAC,MAAM,IAAI,OAAO,KAAK,IAAI,EAAE;gBACtD,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;aAC7D;SACF;QAED;;;WAGG;QACH,IAAI,GAAG,IAAI,CAAC,OAAO,CAAA;QACnB,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,MAAM,CAAC,KAAK,EAAE;YAC1C,OAAO,IAAI,CAAA;SACZ;QAED;;;WAGG;QACH,IAAI,qCAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,yBAAY,CAAC,MAAM,EAAE;YAC1D,OAAO,IAAI,CAAA;SACZ;KACF;AACH,CAAC;AA7DD,kEA6DC"}