{"version": 3, "file": "NodeAlgorithm.js", "sourceRoot": "", "sources": ["../../src/algorithm/NodeAlgorithm.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,0CAAoC;AAIpC,gCAA+B;AAC/B,yCAA6D;AAC7D,qDAI0B;AAC1B,6DAA6E;AAC7E,+CAAoD;AACpD,yDAA0E;AAC1E,uDAA4E;AAE5E;;;;;GAKG;AACH,SAAgB,qBAAqB,CAAC,GAAW,EAAE,MAAY;IAC7D;;;;;OAKG;IACH,IAAI,IAAI,GAAgB,IAAI,CAAA;IAC5B,IAAI,GAAG,KAAK,EAAE,EAAE;QACd,IAAI,GAAG,6BAAW,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC,CAAA;KAC9C;IACD,uCAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;AACnC,CAAC;AAZD,sDAYC;AAED;;;;;;GAMG;AACH,SAAgB,UAAU,CAAC,IAAU,EAAE,QAAgC,EACrE,iBAAkC;;IADG,yBAAA,EAAA,eAAgC;IACrE,kCAAA,EAAA,yBAAkC;IAClC;;OAEG;IACH,IAAI,QAAQ,KAAK,IAAI;QACnB,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAA;IAE/B,IAAI,IAAU,CAAA;IAEd,IAAI,YAAK,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;QAC7B;;;;;;;;WAQG;QACH,IAAI,GAAG,0CAAuB,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,EACtD,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;;YAC1D,KAAwB,IAAA,KAAA,SAAA,IAAI,CAAC,cAAc,CAAA,gBAAA,4BAAE;gBAAxC,IAAM,SAAS,WAAA;gBAClB,IAAM,aAAa,GAAG,UAAU,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;gBACrD,iCAAc,CAAC,aAAqB,EAAE,IAAe,CAAC,CAAA;aACvD;;;;;;;;;KACF;SAAM;QACL;;;;;;;;;;;;;;;;;WAiBG;QACH,IAAI,YAAK,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;YAC9B,IAAM,GAAG,GAAG,iCAAe,EAAE,CAAA;YAC7B,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;YAC9B,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAA;YACpC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;YACpB,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;YAC1B,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;YACtB,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;YACtB,IAAI,GAAG,GAAG,CAAA;SACX;aAAM,IAAI,YAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE;YACzC,IAAM,OAAO,GAAG,qCAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,EACtD,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;YACjC,IAAI,GAAG,OAAO,CAAA;SACf;aAAM,IAAI,YAAK,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;YACjC,IAAM,IAAI,GAAG,6BAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;YAClD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;YACjC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAA;YAC7C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;YACzB,IAAI,GAAG,IAAI,CAAA;SACZ;aAAM,IAAI,YAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;YAC1C,IAAI,GAAG,6BAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;SACzC;aAAM,IAAI,YAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE;YACzC,IAAI,GAAG,qCAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;SACjD;aAAM,IAAI,YAAK,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;YACpC,IAAI,GAAG,gCAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;SAC5C;aAAM,IAAI,YAAK,CAAC,2BAA2B,CAAC,IAAI,CAAC,EAAE;YAClD,IAAI,GAAG,8CAA4B,CAAC,QAAQ,EAC1C,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;SAC5B;aAAM,IAAI,YAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE;YAC7C,IAAI,GAAG,yCAAuB,CAAC,QAAQ,CAAC,CAAA;SACzC;aAAM;YACL,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;SAC3B;KACF;IAED;;;OAGG;IACH,IAAI,YAAK,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QAC9B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QACzB,QAAQ,GAAG,IAAI,CAAA;KAChB;SAAM;QACL,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAA;KAC9B;IAED;;;;OAIG;IACH,IAAI,aAAG,CAAC,QAAQ,CAAC,KAAK,EAAE;QACtB,kCAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,iBAAiB,CAAC,CAAA;KAC7D;IAED;;;;OAIG;IACH,IAAI,iBAAiB,EAAE;;YACrB,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,SAAS,CAAA,gBAAA,4BAAE;gBAA/B,IAAM,KAAK,WAAA;gBACd,IAAM,SAAS,GAAG,UAAU,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;gBACnD,mCAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;aACjC;;;;;;;;;KACF;IAED;;OAEG;IACH,OAAO,IAAI,CAAA;AACb,CAAC;AApHD,gCAoHC;AAED;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,CAAO,EAAE,CAAO;;IAC1C;;OAEG;IACH,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS;QAAE,OAAO,KAAK,CAAA;IAE7C;;;;;;;;;;;;;OAaG;IACH,IAAI,YAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,YAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,EAAE;QAC9D,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS;YACpD,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS;YAAE,OAAO,KAAK,CAAA;KAC5C;SAAM,IAAI,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE;QAC3D,IAAI,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,gBAAgB,KAAK,CAAC,CAAC,gBAAgB;YAC5E,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU;YAC7B,CAAC,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,CAAC,cAAc,CAAC,MAAM;YAAE,OAAO,KAAK,CAAA;KACpE;SAAM,IAAI,YAAK,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,YAAK,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;QACrD,IAAI,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU;YAChE,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM;YAAE,OAAO,KAAK,CAAA;KACtC;SAAM,IAAI,YAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC,IAAI,YAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC,EAAE;QACvF,IAAI,CAAC,CAAC,OAAO,KAAK,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK;YAAE,OAAO,KAAK,CAAA;KACjE;SAAM,IAAI,YAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,YAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE;QACvE,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK;YAAE,OAAO,KAAK,CAAA;KACtC;IAED;;;OAGG;IACH,IAAI,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE;QACpD,IAAM,OAAO,GAA4B,EAAE,CAAA;;YAC3C,KAAoB,IAAA,KAAA,SAAA,CAAC,CAAC,cAAc,CAAA,gBAAA,4BAAE;gBAAjC,IAAM,KAAK,WAAA;gBACd,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,KAAK,CAAA;aAClC;;;;;;;;;;YACD,KAAoB,IAAA,KAAA,SAAA,CAAC,CAAC,cAAc,CAAA,gBAAA,4BAAE;gBAAjC,IAAM,KAAK,WAAA;gBACd,IAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;gBACvC,IAAI,CAAC,KAAK;oBAAE,OAAO,KAAK,CAAA;gBACxB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC;oBAAE,OAAO,KAAK,CAAA;aAC7C;;;;;;;;;KACF;IAED;;;OAGG;IACH,IAAI,CAAC,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,CAAC,SAAS,CAAC,IAAI;QAAE,OAAO,KAAK,CAAA;IACvD,IAAM,GAAG,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAA;IAC1C,IAAM,GAAG,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAA;IAC1C,IAAI,OAAO,GAAG,GAAG,CAAC,IAAI,EAAE,CAAA;IACxB,IAAI,OAAO,GAAG,GAAG,CAAC,IAAI,EAAE,CAAA;IACxB,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;QACrC,IAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAA;QAC5B,IAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAA;QAC5B,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,MAAM,CAAC;YAAE,OAAO,KAAK,CAAA;QAC9C,OAAO,GAAG,GAAG,CAAC,IAAI,EAAE,CAAA;QACpB,OAAO,GAAG,GAAG,CAAC,IAAI,EAAE,CAAA;KACrB;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAtED,kCAsEC;AAED;;;;;;;GAOG;AACH,SAAgB,oCAAoC,CAAC,aAAqB,EAAE,IAAU;IAGpF;;;;;;;;;;;;OAYG;IACH,IAAI,aAAa,KAAK,GAAG,EAAE;QACzB,OAAO,uCAAqB,CAAC,IAAI,CAAC,CAAA;KACnC;SAAM,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,KAAK,MAAM,EAAE;QAC9C,OAAO,uCAAqB,CAAC,IAAI,EAAE,UAAU,GAAG;YAC9C,IAAI,GAAG,CAAC,UAAU,KAAK,iBAAc,CAAC,IAAI;gBACxC,GAAG,CAAC,cAAc,KAAK,aAAa,CAAC,WAAW,EAAE,EAAE;gBACpD,OAAO,IAAI,CAAA;aACZ;iBAAM,IAAI,GAAG,CAAC,UAAU,KAAK,iBAAc,CAAC,IAAI;gBAC/C,GAAG,CAAC,cAAc,KAAK,aAAa,EAAE;gBACtC,OAAO,IAAI,CAAA;aACZ;iBAAM;gBACL,OAAO,KAAK,CAAA;aACb;QACH,CAAC,CAAC,CAAA;KACH;SAAM;QACL,OAAO,uCAAqB,CAAC,IAAI,EAAE,UAAU,GAAG;YAC9C,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,aAAa,CAAC,CAAA;QAC/C,CAAC,CAAC,CAAA;KACH;AAEH,CAAC;AApCD,oFAoCC;AAED;;;;;;;;GAQG;AACH,SAAgB,gCAAgC,CAAC,SAAwB,EACvE,SAAiB,EAAE,IAAU;IAC7B;;;;;;;;;;;;;OAaG;IACH,IAAI,SAAS,KAAK,EAAE;QAAE,SAAS,GAAG,IAAI,CAAA;IAEtC,IAAI,SAAS,KAAK,GAAG,IAAI,SAAS,KAAK,GAAG,EAAE;QAC1C,OAAO,uCAAqB,CAAC,IAAI,CAAC,CAAA;KACnC;SAAM,IAAI,SAAS,KAAK,GAAG,EAAE;QAC5B,OAAO,uCAAqB,CAAC,IAAI,EAAE,UAAU,GAAG;YAC9C,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,SAAS,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;KACH;SAAM,IAAI,SAAS,KAAK,GAAG,EAAE;QAC5B,OAAO,uCAAqB,CAAC,IAAI,EAAE,UAAU,GAAG;YAC9C,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,SAAS,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;KACH;SAAM;QACL,OAAO,uCAAqB,CAAC,IAAI,EAAE,UAAU,GAAG;YAC9C,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,SAAS,IAAI,GAAG,CAAC,UAAU,KAAK,SAAS,CAAC,CAAA;QACvE,CAAC,CAAC,CAAA;KACH;AACH,CAAC;AAjCD,4EAiCC;AAED;;;;;;;;GAQG;AACH,SAAgB,iCAAiC,CAAC,UAAkB,EAAE,IAAU;IAG9E;;;;;;;;;OASG;IAEH,IAAM,OAAO,GAAG,sCAAgB,CAAC,UAAU,CAAC,CAAA;IAC5C,IAAI,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE;QACtB,OAAO,uCAAqB,CAAC,IAAI,EAAE,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC,CAAA;KAChD;IAED,IAAM,aAAa,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAA;IAC7D,OAAO,uCAAqB,CAAC,IAAI,EAAE,UAAU,GAAG;QAC9C,IAAM,UAAU,GAAG,GAAG,CAAC,SAAS,CAAA;QAChC,OAAO,yCAAmB,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,EACtD,aAAa,CAAC,CAAA;IAClB,CAAC,CAAC,CAAA;AAEJ,CAAC;AA1BD,8EA0BC;AAED;;;;;;GAMG;AACH,SAAgB,2BAA2B,CAAC,OAAgB,EAC1D,SAAwB;IACxB;;;OAGG;IACH,IAAI,OAAO,CAAC,UAAU,KAAK,SAAS,IAAI,OAAO,CAAC,gBAAgB,KAAK,IAAI,EAAE;QACzE,OAAO,OAAO,CAAC,gBAAgB,CAAA;KAChC;IAED;;;;OAIG;IACH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACtD,IAAM,IAAI,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;QACtC,IAAI,IAAI,CAAC,gBAAgB,KAAK,OAAO,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,EAAE;YAClE,OAAO,IAAI,CAAC,UAAU,CAAA;SACvB;KACF;IAED;;;OAGG;IACH,IAAI,OAAO,CAAC,OAAO,IAAI,YAAK,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAC3D,OAAO,2BAA2B,CAAC,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;KAC/D;IAGD;;OAEG;IACH,OAAO,IAAI,CAAA;AACb,CAAC;AAnCD,kEAmCC;AAED;;;;;;GAMG;AACH,SAAgB,qBAAqB,CAAC,IAAU,EAAE,MAAqB;IACrE,IAAI,YAAK,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;QAC7B;;;WAGG;QACH,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,IAAI,IAAI,CAAC,gBAAgB,KAAK,MAAM,EAAE;YAChE,OAAO,IAAI,CAAC,UAAU,CAAA;SACvB;QACD;;;;;;WAMG;QACH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACnD,IAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAA;YACnC,IAAI,IAAI,CAAC,UAAU,KAAK,iBAAc,CAAC,KAAK;gBAC1C,IAAI,CAAC,gBAAgB,KAAK,OAAO;gBACjC,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE;gBAC5B,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAA;aAC3B;YACD,IAAI,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,iBAAc,CAAC,KAAK;gBAC7D,IAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,OAAO,EAAE;gBAC/D,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAA;aAC3B;SACF;QAED;;WAEG;QACH,IAAI,IAAI,CAAC,aAAa,KAAK,IAAI;YAAE,OAAO,IAAI,CAAA;QAE5C;;;WAGG;QACH,OAAO,qBAAqB,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,CAAA;KACzD;SAAM,IAAI,YAAK,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QACrC;;;;WAIG;QACH,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI;YAAE,OAAO,IAAI,CAAA;QAC9C,OAAO,qBAAqB,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAA;KAC3D;SAAM,IAAI,YAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,YAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE;QAC/E,OAAO,IAAI,CAAA;KACZ;SAAM,IAAI,YAAK,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;QACjC;;;;WAIG;QACH,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI;YAAE,OAAO,IAAI,CAAA;QACvC,OAAO,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;KACpD;SAAM;QACL;;;;WAIG;QACH,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,YAAK,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;YAAE,OAAO,IAAI,CAAA;QACpE,OAAO,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;KACnD;AAEH,CAAC;AAnED,sDAmEC"}