{"version": 3, "file": "MutationObserverAlgorithm.js", "sourceRoot": "", "sources": ["../../src/algorithm/MutationObserverAlgorithm.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,0CAAoC;AAEpC,gCAA+B;AAC/B,yCAAoE;AACpE,qDAAgF;AAChF,iDAAqF;AACrF,mDAAoD;AAEpD;;;GAGG;AACH,SAAgB,wCAAwC;IACtD;;;;;OAKG;IACH,IAAM,MAAM,GAAG,aAAG,CAAC,MAAM,CAAA;IAEzB,IAAI,MAAM,CAAC,gCAAgC;QAAE,OAAM;IACnD,MAAM,CAAC,gCAAgC,GAAG,IAAI,CAAA;IAC9C,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,cAAQ,gCAAgC,EAAE,CAAA,CAAC,CAAC,CAAC,CAAA;AACtE,CAAC;AAZD,4FAYC;AAED;;GAEG;AACH,SAAgB,gCAAgC;;IAC9C;;;;;OAKG;IACH,IAAM,MAAM,GAAG,aAAG,CAAC,MAAM,CAAA;IAEzB,MAAM,CAAC,gCAAgC,GAAG,KAAK,CAAA;IAC/C,IAAM,SAAS,GAAG,WAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAA;IAC3D,IAAM,SAAS,GAAG,WAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;IACrD,WAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;4BAIxB,EAAE;QACX;;;WAGG;QACH,IAAM,OAAO,GAAG,YAAS,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,CAAA;QAChD,YAAS,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,CAAA;QAChC;;;WAGG;QACH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAM,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;YAC5B,YAAS,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE,UAAC,QAAQ;gBACtD,OAAO,YAAK,CAAC,6BAA6B,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,KAAK,EAAE,CAAA;YAClF,CAAC,CAAC,CAAA;SACH;QACD;;;WAGG;QACH,IAAI,CAAC,YAAS,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAC/B,IAAI;gBACF,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC,CAAA;aACnC;YAAC,OAAO,GAAG,EAAE;gBACZ,6BAA6B;aAC9B;SACF;;;QA9BH;;WAEG;QACH,KAAiB,IAAA,cAAA,SAAA,SAAS,CAAA,oCAAA;YAArB,IAAM,EAAE,sBAAA;oBAAF,EAAE;SA4BZ;;;;;;;;;IACD;;;OAGG;IACH,IAAI,aAAG,CAAC,QAAQ,CAAC,KAAK,EAAE;;YACtB,KAAmB,IAAA,cAAA,SAAA,SAAS,CAAA,oCAAA,2DAAE;gBAAzB,IAAM,IAAI,sBAAA;gBACb,kCAAiB,CAAC,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA;aACpE;;;;;;;;;KACF;AACH,CAAC;AAtDD,4EAsDC;AAED;;;;;;;;;;;;GAYG;AACH,SAAgB,4BAA4B,CAAC,IAAkD,EAC7F,MAAY,EAAE,IAAmB,EACjC,SAAwB,EAAE,QAAuB,EACjD,UAAkB,EAAE,YAAoB,EACxC,eAA4B,EAAE,WAAwB;;IAEtD;;;;;OAKG;IACH,IAAM,mBAAmB,GAAG,IAAI,GAAG,EAAmC,CAAA;IACtE,IAAI,IAAI,GAAG,yCAAyB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IAClD,OAAO,IAAI,KAAK,IAAI,EAAE;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5D,IAAM,UAAU,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAA;YAClD;;;;;;;;;;eAUG;YACH,IAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAA;YAElC,IAAI,IAAI,KAAK,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO;gBAAE,SAAQ;YACjD,IAAI,IAAI,KAAK,YAAY,IAAI,CAAC,OAAO,CAAC,UAAU;gBAAE,SAAQ;YAC1D,IAAI,IAAI,KAAK,YAAY,IAAI,OAAO,CAAC,eAAe;gBAClD,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,IAAI,SAAS,KAAK,IAAI,CAAC;gBAAE,SAAQ;YAChF,IAAI,IAAI,KAAK,eAAe,IAAI,CAAC,OAAO,CAAC,aAAa;gBAAE,SAAQ;YAChE,IAAI,IAAI,KAAK,WAAW,IAAI,CAAC,OAAO,CAAC,SAAS;gBAAE,SAAQ;YAExD;;;;;;;;;eASG;YACH,IAAM,EAAE,GAAG,UAAU,CAAC,QAAQ,CAAA;YAC9B,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;gBAChC,mBAAmB,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;aAClC;YACD,IAAI,CAAC,IAAI,KAAK,YAAY,IAAI,OAAO,CAAC,iBAAiB,CAAC;gBACtD,CAAC,IAAI,KAAK,eAAe,IAAI,OAAO,CAAC,qBAAqB,CAAC,EAAE;gBAC7D,mBAAmB,CAAC,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAA;aACtC;SACF;QACD,IAAI,GAAG,wCAAwB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;KACpD;;QAED;;WAEG;QACH,KAAyC,IAAA,wBAAA,SAAA,mBAAmB,CAAA,wDAAA,yFAAE;YAAnD,IAAA,6CAA0B,EAAzB,gBAAQ,EAAE,sBAAc;YAClC;;;;;;;;eAQG;YACH,IAAM,MAAM,GAAG,uCAAqB,CAAC,IAAI,EAAE,MAAM,EAC/C,uCAAqB,CAAC,MAAM,EAAE,UAAU,CAAC,EACzC,uCAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,EAC3C,eAAe,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc,CAAC,CAAA;YAEhE,IAAM,KAAK,GAAqB,QAAQ,CAAC,YAAY,CAAA;YACrD,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SACnB;;;;;;;;;IAED;;OAEG;IACH,wCAAwC,EAAE,CAAA;AAC5C,CAAC;AArFD,oEAqFC;AAED;;;;;;;;GAQG;AACH,SAAgB,gCAAgC,CAAC,MAAY,EAC3D,UAAkB,EAAE,YAAoB,EACxC,eAA4B,EAAE,WAAwB;IACtD;;;;;OAKG;IACH,4BAA4B,CAAC,WAAW,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAChE,UAAU,EAAE,YAAY,EAAE,eAAe,EAAE,WAAW,CAAC,CAAA;AAC3D,CAAC;AAXD,4EAWC;AAED;;;;;;;GAOG;AACH,SAAgB,qCAAqC,CAAC,MAAY,EAAE,IAAmB,EACrF,SAAwB,EAAE,QAAuB;IACjD;;;;OAIG;IACH,4BAA4B,CAAC,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAChE,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AACjC,CAAC;AATD,sFASC"}