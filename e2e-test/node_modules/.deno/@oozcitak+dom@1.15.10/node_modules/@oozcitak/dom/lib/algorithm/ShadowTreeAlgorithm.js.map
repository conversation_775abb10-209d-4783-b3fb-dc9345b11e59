{"version": 3, "file": "ShadowTreeAlgorithm.js", "sourceRoot": "", "sources": ["../../src/algorithm/ShadowTreeAlgorithm.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,0CAAoC;AAEpC,gCAAqC;AACrC,uCAAwC;AACxC,iDAEwB;AACxB,yEAAsF;AAEtF;;;;GAIG;AACH,SAAgB,4BAA4B,CAAC,IAAU;IACrD;;;OAGG;IACH,IAAM,MAAM,GAAG,aAAG,CAAC,MAAM,CAAA;IACzB,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAC7B,oEAAwC,EAAE,CAAA;AAC5C,CAAC;AARD,oEAQC;AAED;;;;;GAKG;AACH,SAAgB,sBAAsB,CAAC,OAAgB;IACrD;;OAEG;IACH,OAAO,YAAK,CAAC,cAAc,CAAC,6BAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAA;AAC3D,CAAC;AALD,wDAKC;AAED;;;;GAIG;AACH,SAAgB,qBAAqB,CAAC,QAAkB;IACtD;;OAEG;IACH,OAAO,CAAC,QAAQ,CAAC,aAAa,KAAK,IAAI,CAAC,CAAA;AAC1C,CAAC;AALD,sDAKC;AAED;;;;;GAKG;AACH,SAAgB,oBAAoB,CAAC,QAAkB,EAAE,QACxC;IADwC,yBAAA,EAAA,gBACxC;IACf;;;;;;;;OAQG;IACH,IAAM,IAAI,GAAG,WAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;IAClC,IAAM,MAAM,GAAG,IAAI,CAAC,OAAyB,CAAA;IAC7C,IAAI,MAAM,KAAK,IAAI;QAAE,OAAO,IAAI,CAAA;IAChC,IAAM,MAAM,GAAI,MAAM,CAAC,WAAiC,IAAI,IAAI,CAAA;IAChE,IAAI,MAAM,KAAK,IAAI;QAAE,OAAO,IAAI,CAAA;IAChC,IAAI,QAAQ,IAAI,MAAM,CAAC,KAAK,KAAK,MAAM;QAAE,OAAO,IAAI,CAAA;IAEpD,IAAI,KAAK,GAAG,2CAA2B,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,UAAC,CAAC,IAAK,OAAA,YAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAf,CAAe,CAAC,CAAA;IACpF,OAAO,KAAK,KAAK,IAAI,EAAE;QACrB,IAAK,KAAc,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK;YAAE,OAAQ,KAAc,CAAA;QACpE,KAAK,GAAG,0CAA0B,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,UAAC,CAAC,IAAK,OAAA,YAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAf,CAAe,CAAC,CAAA;KACvF;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAzBD,oDAyBC;AAED;;;;GAIG;AACH,SAAgB,wBAAwB,CAAC,IAAU;;IACjD;;;OAGG;IACH,IAAM,MAAM,GAAe,EAAE,CAAA;IAC7B,IAAM,IAAI,GAAG,6BAAa,CAAC,IAAI,CAAC,CAAA;IAChC,IAAI,CAAC,YAAK,CAAC,YAAY,CAAC,IAAI,CAAC;QAAE,OAAO,MAAM,CAAA;IAE5C;;;OAGG;IACH,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAA;;QACvB,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,SAAS,CAAA,gBAAA,4BAAE;YAAlC,IAAM,QAAQ,WAAA;YACjB,IAAI,YAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;gBAC9B;;;mBAGG;gBACH,IAAM,SAAS,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAA;gBAChD,IAAI,SAAS,KAAK,IAAI,EAAE;oBACtB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;iBACtB;aACF;SACF;;;;;;;;;IAED;;OAEG;IACH,OAAO,MAAM,CAAA;AACf,CAAC;AA/BD,4DA+BC;AAED;;;;GAIG;AACH,SAAgB,iCAAiC,CAAC,IAAU;;IAC1D;;;OAGG;IACH,IAAM,MAAM,GAAe,EAAE,CAAA;IAC7B,IAAM,IAAI,GAAG,6BAAa,CAAC,IAAI,CAAC,CAAA;IAChC,IAAI,CAAC,YAAK,CAAC,YAAY,CAAC,IAAI,CAAC;QAAE,OAAO,MAAM,CAAA;IAE5C;;;;OAIG;IACH,IAAM,SAAS,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAA;IAChD,IAAI,cAAO,CAAC,SAAS,CAAC,EAAE;;YACtB,KAAuB,IAAA,KAAA,SAAA,IAAI,CAAC,SAAS,CAAA,gBAAA,4BAAE;gBAAlC,IAAM,QAAQ,WAAA;gBACjB,IAAI,YAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;oBAC9B,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;iBACzB;aACF;;;;;;;;;KACF;;QAED;;WAEG;QACH,KAAmB,IAAA,cAAA,SAAA,SAAS,CAAA,oCAAA,2DAAE;YAAzB,IAAM,IAAI,sBAAA;YACb;;eAEG;YACH,IAAI,YAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,YAAK,CAAC,YAAY,CAAC,6BAAa,CAAC,IAAI,CAAC,CAAC,EAAE;gBACjE;;;mBAGG;gBACH,IAAM,eAAe,GAAG,iCAAiC,CAAC,IAAI,CAAC,CAAA;gBAC/D,MAAM,CAAC,IAAI,OAAX,MAAM,WAAS,eAAe,GAAC;aAChC;iBAAM;gBACL;;mBAEG;gBACH,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;aAClB;SACF;;;;;;;;;IAED;;OAEG;IACH,OAAO,MAAM,CAAA;AACf,CAAC;AAjDD,8EAiDC;AAED;;;;GAIG;AACH,SAAgB,0BAA0B,CAAC,IAAU;;IACnD;;;;OAIG;IACH,IAAM,SAAS,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAA;IAChD,IAAI,SAAS,CAAC,MAAM,KAAK,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE;QACnD,IAAI,cAAc,GAAG,IAAI,CAAA;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACzC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE;gBAC3C,cAAc,GAAG,KAAK,CAAA;gBACtB,MAAK;aACN;SACF;QACD,IAAI,CAAC,cAAc,EAAE;YACnB,4BAA4B,CAAC,IAAI,CAAC,CAAA;SACnC;KACF;IAED;;;OAGG;IACH,IAAI,CAAC,cAAc,GAAG,SAAS,CAAA;;QAC/B,KAAuB,IAAA,cAAA,SAAA,SAAS,CAAA,oCAAA,2DAAE;YAA7B,IAAM,QAAQ,sBAAA;YACjB,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAA;SAC9B;;;;;;;;;AACH,CAAC;AA5BD,gEA4BC;AAED;;;;GAIG;AACH,SAAgB,kCAAkC,CAAC,IAAU;IAC3D;;;OAGG;IACH,IAAI,UAAU,GAAG,2CAA2B,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,UAAC,CAAO,IAAK,OAAA,YAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAf,CAAe,CAAC,CAAA;IAC7F,OAAO,UAAU,KAAK,IAAI,EAAE;QAC1B,0BAA0B,CAAC,UAAkB,CAAC,CAAA;QAC9C,UAAU,GAAG,0CAA0B,CAAC,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,UAAC,CAAO,IAAK,OAAA,YAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAf,CAAe,CAAC,CAAA;KACrG;AACH,CAAC;AAVD,gFAUC;AAED;;;;GAIG;AACH,SAAgB,sBAAsB,CAAC,QAAkB;IACvD;;;OAGG;IACH,IAAM,IAAI,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAA;IAC3C,IAAI,IAAI,KAAK,IAAI,EAAE;QACjB,0BAA0B,CAAC,IAAI,CAAC,CAAA;KACjC;AACH,CAAC;AATD,wDASC"}