{"version": 3, "file": "TreeWalkerImpl.js", "sourceRoot": "", "sources": ["../../src/dom/TreeWalkerImpl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA6D;AAC7D,iDAA+C;AAC/C,0CAEqB;AAErB;;GAEG;AACH;IAAoC,kCAAa;IAI/C;;OAEG;IACH,wBAAoB,IAAU,EAAE,OAAa;QAA7C,YACE,kBAAM,IAAI,CAAC,SAGZ;QADC,KAAI,CAAC,QAAQ,GAAG,OAAO,CAAA;;IACzB,CAAC;IAGD,sBAAI,uCAAW;QADf,kBAAkB;aAClB,cAA0B,OAAO,IAAI,CAAC,QAAQ,CAAA,CAAC,CAAC;aAChD,UAAgB,KAAW,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA,CAAC,CAAC;;;OADN;IAGhD,kBAAkB;IAClB,mCAAU,GAAV;QACE;;;WAGG;QACH,IAAI,IAAI,GAAgB,IAAI,CAAC,QAAQ,CAAA;QACrC,OAAO,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,EAAE;YAC3C;;;;;eAKG;YACH,IAAI,GAAG,IAAI,CAAC,OAAO,CAAA;YACnB,IAAI,IAAI,KAAK,IAAI;gBACf,4BAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,yBAAY,CAAC,MAAM,EAAE;gBACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACpB,OAAO,IAAI,CAAA;aACZ;SACF;QAED;;WAEG;QACH,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,mCAAU,GAAV;QACE;;;WAGG;QACH,OAAO,uCAA2B,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAChD,CAAC;IAED,kBAAkB;IAClB,kCAAS,GAAT;QACE;;;WAGG;QACH,OAAO,uCAA2B,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IACjD,CAAC;IAED,kBAAkB;IAClB,oCAAW,GAAX;QACE;;;WAGG;QACH,OAAO,uCAA2B,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAChD,CAAC;IAED,kBAAkB;IAClB,qCAAY,GAAZ;QACE;;;WAGG;QACH,IAAI,IAAI,GAAgB,IAAI,CAAC,QAAQ,CAAA;QAErC,OAAO,IAAI,KAAK,IAAI,CAAC,KAAK,EAAE;YAC1B;;;eAGG;YACH,IAAI,OAAO,GAAgB,IAAI,CAAC,gBAAgB,CAAA;YAChD,OAAO,OAAO,EAAE;gBACd;;;;mBAIG;gBACH,IAAI,GAAG,OAAO,CAAA;gBACd,IAAI,MAAM,GAAG,4BAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;gBAEzC;;mBAEG;gBACH,OAAO,MAAM,KAAK,yBAAY,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE;oBACxD;;;;uBAIG;oBACH,IAAI,GAAG,IAAI,CAAC,UAAU,CAAA;oBACtB,MAAM,GAAG,4BAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;iBACtC;gBAED;;;mBAGG;gBACH,IAAI,MAAM,KAAK,yBAAY,CAAC,MAAM,EAAE;oBAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;oBACpB,OAAO,IAAI,CAAA;iBACZ;gBAED;;mBAEG;gBACH,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAA;aAChC;YAED;;;eAGG;YACH,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;gBAChD,OAAO,IAAI,CAAA;aACZ;YAED;;eAEG;YACH,IAAI,GAAG,IAAI,CAAC,OAAO,CAAA;YAEnB;;;;eAIG;YACH,IAAI,4BAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,yBAAY,CAAC,MAAM,EAAE;gBACxD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACpB,OAAO,IAAI,CAAA;aACZ;SACF;QAED;;WAEG;QACH,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,wCAAe,GAAf;QACE;;;WAGG;QACH,OAAO,uCAA2B,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IACjD,CAAC;IAED,kBAAkB;IAClB,iCAAQ,GAAR;QACE;;;;WAIG;QACH,IAAI,IAAI,GAAgB,IAAI,CAAC,QAAQ,CAAA;QACrC,IAAI,MAAM,GAAG,yBAAY,CAAC,MAAM,CAAA;QAEhC,OAAO,IAAI,EAAE;YACX;;eAEG;YACH,OAAO,MAAM,KAAK,yBAAY,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE;gBACzD;;;;;;mBAMG;gBACH,IAAI,GAAG,IAAI,CAAC,WAAW,CAAA;gBACvB,MAAM,GAAG,4BAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;gBACrC,IAAI,MAAM,KAAK,yBAAY,CAAC,MAAM,EAAE;oBAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;oBACpB,OAAO,IAAI,CAAA;iBACZ;aACF;YAED;;;;eAIG;YACH,IAAI,OAAO,GAAgB,IAAI,CAAA;YAC/B,IAAI,SAAS,GAAgB,IAAI,CAAA;YACjC,OAAO,SAAS,KAAK,IAAI,EAAE;gBACzB;;mBAEG;gBACH,IAAI,SAAS,KAAK,IAAI,CAAC,KAAK,EAAE;oBAC5B,OAAO,IAAI,CAAA;iBACZ;gBACD;;;mBAGG;gBACH,OAAO,GAAG,SAAS,CAAC,YAAY,CAAA;gBAChC,IAAI,OAAO,KAAK,IAAI,EAAE;oBACpB,IAAI,GAAG,OAAO,CAAA;oBACd,MAAK;iBACN;gBACD;;mBAEG;gBACH,SAAS,GAAG,SAAS,CAAC,OAAO,CAAA;aAC9B;YAED;;;;eAIG;YACH,MAAM,GAAG,4BAAgB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YACrC,IAAI,MAAM,KAAK,yBAAY,CAAC,MAAM,EAAE;gBAClC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACpB,OAAO,IAAI,CAAA;aACZ;SACF;IACH,CAAC;IAED;;;;;OAKG;IACI,sBAAO,GAAd,UAAe,IAAU,EAAE,OAAa;QACtC,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IAC1C,CAAC;IAEH,qBAAC;AAAD,CAAC,AApPD,CAAoC,6BAAa,GAoPhD;AApPY,wCAAc"}