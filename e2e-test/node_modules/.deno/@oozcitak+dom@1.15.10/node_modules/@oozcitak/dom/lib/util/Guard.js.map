{"version": 3, "file": "Guard.js", "sourceRoot": "", "sources": ["../../src/util/Guard.ts"], "names": [], "mappings": ";;AAAA,gDAK0B;AAE1B;;GAEG;AACH;IAAA;IA0MA,CAAC;IAxMC;;;;OAIG;IACI,YAAM,GAAb,UAAc,CAAM;QAClB,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,CAAA;IAC3C,CAAC;IAED;;;;OAIG;IACI,oBAAc,GAArB,UAAsB,CAAM;QAC1B,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,qBAAQ,CAAC,QAAQ,CAAC,CAAA;IAC/D,CAAC;IAED;;;;OAIG;IACI,wBAAkB,GAAzB,UAA0B,CAAM;QAC9B,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,qBAAQ,CAAC,YAAY,CAAC,CAAA;IACnE,CAAC;IAED;;;;OAIG;IACI,4BAAsB,GAA7B,UAA8B,CAAM;QAClC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,qBAAQ,CAAC,gBAAgB,CAAC,CAAA;IACvE,CAAC;IAED;;;;OAIG;IACI,gBAAU,GAAjB,UAAkB,CAAM;QACtB,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,qBAAQ,CAAC,SAAS,CAAC,CAAA;IAChE,CAAC;IAED;;;;OAIG;IACI,yBAAmB,GAA1B,UAA2B,CAAM;QAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;YAAE,OAAO,KAAK,CAAA;QAElC,IAAM,IAAI,GAAG,CAAC,CAAC,SAAS,CAAA;QAExB,OAAO,CAAC,IAAI,KAAK,qBAAQ,CAAC,IAAI;YAC5B,IAAI,KAAK,qBAAQ,CAAC,qBAAqB;YACvC,IAAI,KAAK,qBAAQ,CAAC,OAAO;YACzB,IAAI,KAAK,qBAAQ,CAAC,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED;;;;OAIG;IACI,gBAAU,GAAjB,UAAkB,CAAM;QACtB,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,KAAK,qBAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,SAAS,KAAK,qBAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;IAC/F,CAAC;IAED;;;;OAIG;IACI,yBAAmB,GAA1B,UAA2B,CAAM;QAC/B,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,qBAAQ,CAAC,IAAI,CAAC,CAAA;IAC3D,CAAC;IAED;;;;OAIG;IACI,wBAAkB,GAAzB,UAA0B,CAAM;QAC9B,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,qBAAQ,CAAC,KAAK,CAAC,CAAA;IAC5D,CAAC;IAED;;;;OAIG;IACI,mBAAa,GAApB,UAAqB,CAAM;QACzB,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,qBAAQ,CAAC,OAAO,CAAC,CAAA;IAC9D,CAAC;IAED;;;;OAIG;IACI,iCAA2B,GAAlC,UAAmC,CAAM;QACvC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,qBAAQ,CAAC,qBAAqB,CAAC,CAAA;IAC5E,CAAC;IAED;;;;OAIG;IACI,mBAAa,GAApB,UAAqB,CAAM;QACzB,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,qBAAQ,CAAC,OAAO,CAAC,CAAA;IAC9D,CAAC;IAED;;;;OAIG;IACI,yBAAmB,GAA1B,UAA2B,CAAM;QAC/B,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,mBAAmB,KAAK,QAAQ,CAAC,CAAA;IACvE,CAAC;IAED;;;;OAIG;IACI,kBAAY,GAAnB,UAAoB,CAAM;QACxB,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,CAAA;IACtC,CAAC;IAED;;;;OAIG;IACI,kBAAY,GAAnB,UAAoB,CAAM;QACxB,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,SAAS,IAAI,CAAC,CAAC,OAAO,IAAI,SAAS,CAAC,CAAA;IACnE,CAAC;IAED;;;;;;;OAOG;IACI,gBAAU,GAAjB,UAAkB,CAAM;QACtB,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,SAAS,IAAI,CAAC,CAAC,aAAa,KAAK,SAAS;YACnE,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IACpD,CAAC;IAED;;;;OAIG;IACI,YAAM,GAAb,UAAc,CAAM;QAClB,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,SAAS,IAAI,CAAC,CAAC,cAAc,KAAK,SAAS;YACpE,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAA;IAC3B,CAAC;IAED;;;;OAIG;IACI,cAAQ,GAAf,UAAgB,CAAM;QACpB,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,CAAA;IAC3C,CAAC;IAED;;;;OAIG;IACI,qBAAe,GAAtB,UAAuB,CAAM;QAC3B,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,KAAK,SAAS,CAAC,CAAA;IAC7C,CAAC;IAED;;;;OAIG;IACI,0BAAoB,GAA3B,UAA4B,CAAM;QAChC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,KAAK,SAAS,IAAI,CAAC,CAAC,OAAO,KAAK,SAAS,CAAC,CAAA;IACrE,CAAC;IAED;;;;KAIC;IACM,mCAA6B,GAApC,UAAqC,CAAM;QACzC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,SAAS,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAA;IACzE,CAAC;IACH,YAAC;AAAD,CAAC,AA1MD,IA0MC;AA1MY,sBAAK"}