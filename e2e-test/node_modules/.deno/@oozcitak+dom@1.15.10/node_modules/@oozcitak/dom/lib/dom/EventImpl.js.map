{"version": 3, "file": "EventImpl.js", "sourceRoot": "", "sources": ["../../src/dom/EventImpl.ts"], "names": [], "mappings": ";;AAAA,2CAEqB;AACrB,0CAAyE;AACzE,gEAA8D;AAE9D;;GAEG;AACH;IAkCE;;OAEG;IACH,mBAAmB,IAAY,EAAE,SAAqB;QAzBtD,YAAO,GAAyB,IAAI,CAAA;QACpC,mBAAc,GAAyB,IAAI,CAAA;QAC3C,qBAAgB,GAA2B,EAAE,CAAA;QAC7C,UAAK,GAAoB,EAAE,CAAA;QAC3B,mBAAc,GAAyB,IAAI,CAAA;QAC3C,gBAAW,GAAe,uBAAU,CAAC,IAAI,CAAA;QAEzC,yBAAoB,GAAY,KAAK,CAAA;QACrC,kCAA6B,GAAY,KAAK,CAAA;QAC9C,kBAAa,GAAY,KAAK,CAAA;QAC9B,2BAAsB,GAAY,KAAK,CAAA;QACvC,kBAAa,GAAY,KAAK,CAAA;QAC9B,qBAAgB,GAAY,KAAK,CAAA;QACjC,kBAAa,GAAY,KAAK,CAAA;QAE9B,eAAU,GAAY,KAAK,CAAA;QAG3B,aAAQ,GAAY,KAAK,CAAA;QACzB,gBAAW,GAAY,KAAK,CAAA;QAQ1B;;;;;;;;WAQG;QACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QACjB,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,OAAO,IAAI,KAAK,CAAA;YAC1C,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,UAAU,IAAI,KAAK,CAAA;YAChD,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,QAAQ,IAAI,KAAK,CAAA;SACjD;QACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAA;QAC5B,IAAI,CAAC,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAA;IACxC,CAAC;IAGD,sBAAI,2BAAI;QADR,kBAAkB;aAClB,cAAqB,OAAO,IAAI,CAAC,KAAK,CAAA,CAAC,CAAC;;;OAAA;IAGxC,sBAAI,6BAAM;QADV,kBAAkB;aAClB,cAAmC,OAAO,IAAI,CAAC,OAAO,CAAA,CAAC,CAAC;;;OAAA;IAGxD,sBAAI,iCAAU;QADd,kBAAkB;aAClB,cAAuC,OAAO,IAAI,CAAC,OAAO,CAAA,CAAC,CAAC;;;OAAA;IAG5D,sBAAI,oCAAa;QADjB,kBAAkB;aAClB,cAA0C,OAAO,IAAI,CAAC,cAAc,CAAA,CAAC,CAAC;;;OAAA;IAEtE,kBAAkB;IAClB,gCAAY,GAAZ;QAEE;;;;;;;;;WASG;QACH,IAAM,YAAY,GAAkB,EAAE,CAAA;QAEtC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAA;QAEvB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,YAAY,CAAA;QAE1C,IAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAA;QACzC,IAAI,aAAa,KAAK,IAAI,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;SAChD;QACD,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;QAEhC,IAAI,kBAAkB,GAAG,CAAC,CAAA;QAC1B,IAAI,+BAA+B,GAAG,CAAC,CAAA;QAEvC;;;WAGG;QACH,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;QAC3B,OAAO,KAAK,IAAI,CAAC,EAAE;YACjB;;;;;;;;eAQG;YACH,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE;gBAChC,+BAA+B,EAAE,CAAA;aAClC;YACD,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,KAAK,aAAa,EAAE;gBAClD,kBAAkB,GAAG,KAAK,CAAA;gBAC1B,MAAK;aACN;YACD,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE;gBAChC,+BAA+B,EAAE,CAAA;aAClC;YACD,KAAK,EAAE,CAAA;SACR;QAED;;;WAGG;QACH,IAAI,kBAAkB,GAAG,+BAA+B,CAAA;QACxD,IAAI,cAAc,GAAG,+BAA+B,CAAA;QAEpD;;;WAGG;QACH,KAAK,GAAG,kBAAkB,GAAG,CAAC,CAAA;QAC9B,OAAO,KAAK,IAAI,CAAC,EAAE;YACjB;;;;;eAKG;YACH,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE;gBAChC,kBAAkB,EAAE,CAAA;aACrB;YAED,IAAI,kBAAkB,IAAI,cAAc,EAAE;gBACxC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,CAAA;aACnD;YAED;;eAEG;YACH,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE;gBAChC;;;;mBAIG;gBACH,kBAAkB,EAAE,CAAA;gBACpB,IAAI,kBAAkB,GAAG,cAAc,EAAE;oBACvC,cAAc,GAAG,kBAAkB,CAAA;iBACpC;aACF;YAED;;eAEG;YACH,KAAK,EAAE,CAAA;SACR;QAED;;;WAGG;QACH,kBAAkB,GAAG,+BAA+B,CAAA;QACpD,cAAc,GAAG,+BAA+B,CAAA;QAEhD;;;WAGG;QACH,KAAK,GAAG,kBAAkB,GAAG,CAAC,CAAA;QAC9B,OAAO,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;YAC1B;;;;;eAKG;YACH,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE;gBAChC,kBAAkB,EAAE,CAAA;aACrB;YAED,IAAI,kBAAkB,IAAI,cAAc,EAAE;gBACxC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,CAAA;aAChD;YAED;;eAEG;YACH,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE;gBAChC;;;;mBAIG;gBACH,kBAAkB,EAAE,CAAA;gBACpB,IAAI,kBAAkB,GAAG,cAAc,EAAE;oBACvC,cAAc,GAAG,kBAAkB,CAAA;iBACpC;aACF;YAED;;eAEG;YACH,KAAK,EAAE,CAAA;SACR;QAED;;WAEG;QACH,OAAO,YAAY,CAAA;IACrB,CAAC;IAGD,sBAAI,iCAAU;QADd,kBAAkB;aAClB,cAA+B,OAAO,IAAI,CAAC,WAAW,CAAA,CAAC,CAAC;;;OAAA;IAExD,kBAAkB;IAClB,mCAAe,GAAf,cAA0B,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAA,CAAC,CAAC;IAG5D,sBAAI,mCAAY;QADhB,kBAAkB;aAClB,cAA8B,OAAO,IAAI,CAAC,oBAAoB,CAAA,CAAC,CAAC;aAChE,UAAiB,KAAc,IAAI,IAAI,KAAK;YAAE,IAAI,CAAC,eAAe,EAAE,CAAA,CAAC,CAAC;;;OADN;IAGhE,kBAAkB;IAClB,4CAAwB,GAAxB;QACE,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAA;QAChC,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAA;IAC3C,CAAC;IAGD,sBAAI,8BAAO;QADX,kBAAkB;aAClB,cAAyB,OAAO,IAAI,CAAC,QAAQ,CAAA,CAAC,CAAC;;;OAAA;IAG/C,sBAAI,iCAAU;QADd,kBAAkB;aAClB,cAA4B,OAAO,IAAI,CAAC,WAAW,CAAA,CAAC,CAAC;;;OAAA;IAGrD,sBAAI,kCAAW;QADf,kBAAkB;aAClB,cAA6B,OAAO,CAAC,IAAI,CAAC,aAAa,CAAA,CAAC,CAAC;aACzD,UAAgB,KAAc;YAC5B,IAAI,CAAC,KAAK,EAAE;gBACV,oCAAwB,CAAC,IAAI,CAAC,CAAA;aAC/B;QACH,CAAC;;;OALwD;IAOzD,kBAAkB;IAClB,kCAAc,GAAd;QACE,oCAAwB,CAAC,IAAI,CAAC,CAAA;IAChC,CAAC;IAGD,sBAAI,uCAAgB;QADpB,kBAAkB;aAClB,cAAkC,OAAO,IAAI,CAAC,aAAa,CAAA,CAAC,CAAC;;;OAAA;IAG7D,sBAAI,+BAAQ;QADZ,kBAAkB;aAClB,cAA0B,OAAO,IAAI,CAAC,aAAa,CAAA,CAAC,CAAC;;;OAAA;IAGrD,sBAAI,gCAAS;QADb,kBAAkB;aAClB,cAA2B,OAAO,IAAI,CAAC,UAAU,CAAA,CAAC,CAAC;;;OAAA;IAGnD,sBAAI,gCAAS;QADb,kBAAkB;aAClB,cAA0B,OAAO,IAAI,CAAC,UAAU,CAAA,CAAC,CAAC;;;OAAA;IAElD,kBAAkB;IAClB,6BAAS,GAAT,UAAU,IAAY,EAAE,OAAe,EAAE,UAAkB;QAAnC,wBAAA,EAAA,eAAe;QAAE,2BAAA,EAAA,kBAAkB;QACzD;;WAEG;QACH,IAAI,IAAI,CAAC,aAAa;YAAE,OAAM;QAE9B;;WAEG;QACH,4BAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,CAAC,CAAA;IACnD,CAAC;IA5RM,cAAI,GAAG,CAAC,CAAA;IACR,yBAAe,GAAG,CAAC,CAAA;IACnB,mBAAS,GAAG,CAAC,CAAA;IACb,wBAAc,GAAG,CAAC,CAAA;IA2R3B,gBAAC;CAAA,AAhSD,IAgSC;AAhSY,8BAAS;AAkStB;;GAEG;AACH,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,EAAG,CAAC,CAAC,CAAA;AAChD,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,iBAAiB,EAAG,CAAC,CAAC,CAAA;AAC3D,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,WAAW,EAAG,CAAC,CAAC,CAAA;AACrD,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,gBAAgB,EAAG,CAAC,CAAC,CAAA"}