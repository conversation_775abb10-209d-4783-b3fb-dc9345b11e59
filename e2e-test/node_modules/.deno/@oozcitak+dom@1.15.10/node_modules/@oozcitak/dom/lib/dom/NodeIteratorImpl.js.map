{"version": 3, "file": "NodeIteratorImpl.js", "sourceRoot": "", "sources": ["../../src/dom/NodeIteratorImpl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,iDAA+C;AAC/C,0CAA+E;AAE/E;;;GAGG;AACH;IAAsC,oCAAa;IAMjD;;OAEG;IACH,0BAAoB,IAAU,EAAE,SAAe,EAAE,sBAA+B;QAAhF,YACE,kBAAM,IAAI,CAAC,SAOZ;QALC,KAAI,CAAC,mBAAmB,GAAG,SAAkC,CAAA;QAC7D,KAAI,CAAC,UAAU,GAAG,SAAS,CAAA;QAC3B,KAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAA;QAErD,qCAAyB,EAAE,CAAC,GAAG,CAAC,KAAI,CAAC,CAAA;;IACvC,CAAC;IAGD,sBAAI,2CAAa;QADjB,kBAAkB;aAClB,cAA4B,OAAO,IAAI,CAAC,UAAU,CAAA,CAAC,CAAC;;;OAAA;IAGpD,sBAAI,wDAA0B;QAD9B,kBAAkB;aAClB,cAA4C,OAAO,IAAI,CAAC,uBAAuB,CAAA,CAAC,CAAC;;;OAAA;IAEjF,kBAAkB;IAClB,mCAAQ,GAAR;QACE;;;WAGG;QACH,OAAO,iCAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC1C,CAAC;IAED,kBAAkB;IAClB,uCAAY,GAAZ;QACE;;;WAGG;QACH,OAAO,iCAAqB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IAC3C,CAAC;IAED,kBAAkB;IAClB,iCAAM,GAAN;QACE;;;;WAIG;QACH,qCAAyB,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC1C,CAAC;IAGD;;;;;;;OAOG;IACI,wBAAO,GAAd,UAAe,IAAU,EAAE,SAAe,EAAE,sBAA+B;QAEzE,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,SAAS,EAAE,sBAAsB,CAAC,CAAA;IACtE,CAAC;IACH,uBAAC;AAAD,CAAC,AAlED,CAAsC,6BAAa,GAkElD;AAlEY,4CAAgB"}