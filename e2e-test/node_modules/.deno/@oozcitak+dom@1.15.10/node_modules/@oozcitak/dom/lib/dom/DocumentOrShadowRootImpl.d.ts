import { DocumentOrShadowRoot } from "./interfaces";
/**
 * Represents a mixin for an interface to be used to share APIs between
 * documents and shadow roots. This mixin is implemented by
 * {@link Document} and {@link ShadowRoot}.
 *
 * _Note:_ The DocumentOrShadowRoot mixin is expected to be used by other
 * standards that want to define APIs shared between documents and shadow roots.
 */
export declare class DocumentOrShadowRootImpl implements DocumentOrShadowRoot {
}
