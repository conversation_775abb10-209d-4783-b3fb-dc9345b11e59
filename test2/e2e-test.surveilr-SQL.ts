#!/usr/bin/env -S deno run --allow-read --allow-write --allow-env --allow-sys
/**
 * e2e-test.surveilr-SQL.ts - Deno Capturable Executable for surveilr
 *
 * PURPOSE:
 * This is a minimal Capturable Executable (CE) that surveilr can discover and execute
 * to process documents. It emits SQL to stdout for surveilr to capture.
 *
 * EXECUTION MODEL:
 * - No command-line arguments or environment variables
 * - Processes files in ./lib/assurance/test-fixtures/ directory  
 * - Emits SQL statements to stdout (surveilr captures these)
 * - All business logic contained within this script
 *
 * USAGE:
 *     # Direct execution (for testing)
 *     deno run -A e2e-test.surveilr-SQL.ts > output.sql
 *     
 *     # surveilr execution (production)  
 *     # surveilr automatically discovers and runs this CE
 */

import { walk } from "https://deno.land/std@0.224.0/fs/walk.ts";
import { extname, basename } from "https://deno.land/std@0.224.0/path/mod.ts";
import { crypto } from "https://deno.land/std@0.224.0/crypto/mod.ts";
import { encodeHex } from "https://deno.land/std@0.224.0/encoding/hex.ts";

interface ProcessedFile {
  uri: string;
  content: string;
  nature: string;
  size: number;
  hash: string;
  lastModified: Date;
}

function generateId(): string {
  return crypto.randomUUID().replace(/-/g, '').toUpperCase().substring(0, 26);
}

async function hashContent(content: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(content);
  const hashBuffer = await crypto.subtle.digest("SHA-256", data);
  return "sha256:" + encodeHex(hashBuffer);
}

function getMimeType(extension: string): string {
  const mimeTypes: Record<string, string> = {
    '.md': 'text/markdown',
    '.txt': 'text/plain',
    '.json': 'application/json',
    '.pdf': 'application/pdf',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.html': 'text/html',
    '.xml': 'application/xml'
  };
  return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
}

async function processFile(filePath: string): Promise<ProcessedFile> {
  const content = await Deno.readTextFile(filePath);
  const stat = await Deno.stat(filePath);
  const ext = extname(filePath);
  
  return {
    uri: filePath,
    content,
    nature: getMimeType(ext),
    size: content.length,
    hash: await hashContent(content),
    lastModified: stat.mtime || new Date()
  };
}

function createTransform(file: ProcessedFile, transformId: string, resourceId: string): string {
  const ext = extname(file.uri);
  let transformContent: string;
  let transformNature: string;
  let elaboration: string;

  if (ext === '.md') {
    // Markdown pass-through processing
    const passthrough = {
      success: true,
      doc_data: {
        source: file.uri,
        texts: [{
          text: file.content,
          label: "passthrough"
        }],
        tables: [],
        pictures: [],
        extraction_info: {
          processor: "deno-passthrough",
          reason: "markdown_passthrough"
        }
      },
      markdown: file.content,
      processor: "deno-passthrough",
      version: "1.0"
    };
    transformContent = JSON.stringify(passthrough, null, 2);
    transformNature = 'application/json';
    elaboration = 'Deno markdown pass-through processing';
  } else {
    // Simple text extraction for other formats
    const extraction = {
      success: true,
      extracted_text: file.content,
      processor: "deno-text-extractor",
      version: "1.0",
      metadata: {
        file_type: ext,
        size_bytes: file.size,
        processed_at: new Date().toISOString()
      }
    };
    transformContent = JSON.stringify(extraction, null, 2);
    transformNature = 'application/json';
    elaboration = 'Deno text extraction processing';
  }

  const transformHash = crypto.randomUUID(); // Simplified for demo
  const transformUri = `${file.uri}.deno-transform.json`;

  return `INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration) 
VALUES ('${transformId}', '${resourceId}', '${transformUri}', 'sha256:${transformHash}', '${transformContent.replace(/'/g, "''")}', '${transformNature}', ${transformContent.length}, '${elaboration}');`;
}

async function main(): Promise<void> {
  // Get the path relative to this script's location
  const testFixturesPath = new URL('../test/generated/test', import.meta.url).pathname;
  const deviceId = generateId();
  const sessionId = `session-${new Date().toISOString().replace(/[:.]/g, '-')}`;
  const hostname = Deno.hostname();
  
  console.log("BEGIN;");
  console.log();

  // Device setup
  console.log(`-- Device and session setup`);
  console.log(`INSERT OR IGNORE INTO device (device_id, name, state, boundary) 
VALUES ('${deviceId}', '${hostname}-device', 'SINGLETON', 'UNKNOWN');`);
  console.log();

  // Session setup
  const sessionBehavior = JSON.stringify({
    name: "deno-document-processor",
    version: "1.0.0",
    type: "document_extraction",
    runtime: "deno"
  });

  const now = new Date().toISOString();
  console.log(`INSERT INTO ur_ingest_session (ur_ingest_session_id, device_id, behavior_id, behavior_json, ingest_started_at, ingest_finished_at) 
VALUES ('${sessionId}', '${deviceId}', 'deno-processor', '${sessionBehavior}', '${now}', '${now}');`);
  console.log();

  try {
    // Process files
    let fileCount = 0;
    for await (const entry of walk(testFixturesPath, { 
      includeFiles: true, 
      includeDirs: false,
      exts: [".md", ".txt", ".json", ".html"] // Supported text formats
    })) {
      const file = await processFile(entry.path);
      const resourceId = `ur-${String(++fileCount).padStart(3, '0')}`;
      const transformId = `urt-${String(fileCount).padStart(3, '0')}`;

      console.log(`-- Processing: ${file.uri}`);
      console.log(`INSERT INTO uniform_resource (uniform_resource_id, device_id, ur_ingest_session_id, uri, content_digest, content, nature, size_bytes, last_modified_at, content_hash) 
VALUES ('${resourceId}', '${deviceId}', '${sessionId}', '${file.uri}', '${file.hash}', '${file.content.replace(/'/g, "''")}', '${file.nature}', ${file.size}, '${file.lastModified.toISOString()}', '${file.hash}');`);
      console.log();

      // Create transformation
      console.log(createTransform(file, transformId, resourceId));
      console.log();
    }

    if (fileCount === 0) {
      console.log(`-- No processable files found in ${testFixturesPath}`);
    }

  } catch (error) {
    console.log(`-- Error processing files: ${error.message}`);
  }

  console.log("COMMIT;");
}

if (import.meta.main) {
  await main();
}