#!/usr/bin/env -S deno run --allow-read --allow-write --allow-env --allow-sys --allow-run
/**
 * e2e-test.surveilr-SQL.ts - Deno Capturable Executable for surveilr
 *
 * PURPOSE:
 * This is a minimal Capturable Executable (CE) that surveilr can discover and execute
 * to process documents. It emits SQL to stdout for surveilr to capture.
 *
 * EXECUTION MODEL:
 * - No command-line arguments or environment variables
 * - Processes files in ./lib/assurance/test-fixtures/ directory  
 * - Emits SQL statements to stdout (surveilr captures these)
 * - All business logic contained within this script
 *
 * USAGE:
 *     # Direct execution (for testing)
 *     deno run -A e2e-test.surveilr-SQL.ts > output.sql
 *     
 *     # surveilr execution (production)  
 *     # surveilr automatically discovers and runs this CE
 */

import { walk } from "jsr:@std/fs@^1.0.0";
import { extname, basename } from "jsr:@std/path@^1.0.0";
import { crypto } from "jsr:@std/crypto@^1.0.0";
import { encodeHex } from "jsr:@std/encoding@^1.0.0";

interface ProcessedFile {
  uri: string;
  content: string;
  nature: string;
  size: number;
  hash: string;
  lastModified: Date;
}

function generateId(): string {
  return crypto.randomUUID().replace(/-/g, '').toUpperCase().substring(0, 26);
}


async function hashContent(content: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(content);
  const hashBuffer = await crypto.subtle.digest("SHA-256", data);
  return "sha256:" + encodeHex(hashBuffer);
}

function getMimeType(extension: string): string {
  const mimeTypes: Record<string, string> = {
    '.md': 'text/markdown',
    '.txt': 'text/plain',
    '.json': 'application/json',
    '.pdf': 'application/pdf',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.html': 'text/html',
    '.xml': 'application/xml'
  };
  return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
}

async function processFile(filePath: string): Promise<ProcessedFile> {
  const content = await Deno.readTextFile(filePath);
  const stat = await Deno.stat(filePath);
  const ext = extname(filePath);
  
  return {
    uri: filePath,
    content,
    nature: getMimeType(ext),
    size: content.length,
    hash: await hashContent(content),
    lastModified: stat.mtime || new Date()
  };
}

function createTransform(file: ProcessedFile, _transformId: string, resourceId: string): string {
  const ext = extname(file.uri);
  const fileName = basename(file.uri);
  let transformContent: string;
  let transformNature: string;
  let elaboration: string;
  let result = '';

  if (ext === '.md') {
    // Create docling JSON transform
    const doclingData = {
      success: true,
      doc_data: {
        source: file.uri,
        texts: [{
          text: file.content,
          label: "passthrough"
        }],
        tables: [],
        pictures: [],
        extraction_info: {
          processor: "deno-passthrough",
          reason: "markdown_passthrough"
        }
      },
      markdown: file.content,
      processor: "deno-passthrough",
      version: "1.0"
    };
    transformContent = JSON.stringify(doclingData, null, 2);
    transformNature = 'application/json';
    elaboration = JSON.stringify({
      transform_type: "docling_structured_data",
      processor: "docling",
      success: true
    });

    const transformHash = crypto.randomUUID().replace(/-/g, '');
    const transformUri = `${file.uri}.docling.json`;
    const transformId1 = generateId();

    result += `INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration, created_by) VALUES ('${transformId1}', '${resourceId}', '${transformUri}', '${transformHash}', NULL, '${transformNature}', ${transformContent.length}, '${elaboration}', '''UNKNOWN''');\n`;
    result += `UPDATE "uniform_resource_transform" SET "content" = '${transformContent.replace(/'/g, "''")}' WHERE "uniform_resource_transform_id" = '${transformId1}';\n`;

    // Create markdown transform
    const markdownElaboration = JSON.stringify({
      transform_type: "docling_markdown",
      processor: "docling",
      success: true
    });
    const markdownTransformId = generateId();
    const markdownUri = `${file.uri}.docling.md`;
    const markdownHash = crypto.randomUUID().replace(/-/g, '');

    result += `INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration, created_by) VALUES ('${markdownTransformId}', '${resourceId}', '${markdownUri}', '${markdownHash}', NULL, 'text/markdown', ${file.content.length}, '${markdownElaboration}', '''UNKNOWN''');\n`;
    result += `UPDATE "uniform_resource_transform" SET "content" = '${file.content.replace(/'/g, "''")}' WHERE "uniform_resource_transform_id" = '${markdownTransformId}';`;

  } else {
    // Simple fallback processing for other formats
    const fallbackData = {
      source: file.uri,
      texts: [{
        text: file.content,
        label: "fallback"
      }],
      tables: [],
      pictures: [],
      extraction_info: {
        processor: "fallback",
        reason: `File format not allowed: ${file.uri}`
      }
    };
    transformContent = JSON.stringify(fallbackData, null, 2);
    transformNature = 'application/json';
    elaboration = JSON.stringify({
      transform_type: "docling_structured_data",
      processor: "fallback",
      success: false
    });

    const transformHash = crypto.randomUUID().replace(/-/g, '');
    const transformUri = `${file.uri}.docling.json`;
    const transformId1 = generateId();

    result += `INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration, created_by) VALUES ('${transformId1}', '${resourceId}', '${transformUri}', '${transformHash}', NULL, '${transformNature}', ${transformContent.length}, '${elaboration}', '''UNKNOWN''');\n`;
    result += `UPDATE "uniform_resource_transform" SET "content" = '${transformContent.replace(/'/g, "''")}' WHERE "uniform_resource_transform_id" = '${transformId1}';\n`;

    // Create markdown fallback
    const fallbackMarkdown = `# ${fileName}\n\n${file.content}`;
    const markdownElaboration = JSON.stringify({
      transform_type: "docling_markdown",
      processor: "fallback",
      success: false
    });
    const markdownTransformId = generateId();
    const markdownUri = `${file.uri}.docling.md`;
    const markdownHash = crypto.randomUUID().replace(/-/g, '');

    result += `INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration, created_by) VALUES ('${markdownTransformId}', '${resourceId}', '${markdownUri}', '${markdownHash}', NULL, 'text/markdown', ${fallbackMarkdown.length}, '${markdownElaboration}', '''UNKNOWN''');\n`;
    result += `UPDATE "uniform_resource_transform" SET "content" = '${fallbackMarkdown.replace(/'/g, "''")}' WHERE "uniform_resource_transform_id" = '${markdownTransformId}';`;
  }

  return result;
}

async function runPrerequisiteCommand(): Promise<void> {
  console.log("-- Running prerequisite: deno run -A poml-build.surveilr.ts");

  const command = new Deno.Command("deno", {
    args: ["run", "-A", "./poml-build.surveilr.ts"],
    cwd: new URL('.', import.meta.url).pathname,
    stdout: "piped",
    stderr: "piped"
  });

  const process = command.spawn();
  const { code, stdout } = await process.output();

  if (code !== 0) {
    // const errorText = new TextDecoder().decode(stderr);
    // throw new Error(`Prerequisite command failed with code ${code}: ${errorText}`);
  }

  const outputText = new TextDecoder().decode(stdout);
  // console.log("-- Prerequisite command completed successfully");
  if (outputText.trim()) {
    // console.log("-- Output:", outputText.trim());
  }
}

async function main(): Promise<void> {
  // Run prerequisite command first and wait for completion
  await runPrerequisiteCommand();

  // Get the path relative to this script's location
  const testFixturesPath = new URL('./generated', import.meta.url).pathname;
  const deviceId = "01K3HP14NN7H947E2WZ92G0QZS";
  const sessionId = generateId();

  // Count files first
  let totalFileCount = 0;
  for await (const _entry of walk(testFixturesPath, {
    includeFiles: true,
    includeDirs: false,
    exts: [".md", ".txt", ".json", ".html"] // Supported text formats
  })) {
    totalFileCount++;
  }

  console.log(`-- Found ${totalFileCount} files to process;`);
  console.log("BEGIN;");

  // Session setup - satisfies FOREIGN KEY("ingest_session_id") REFERENCES "ur_ingest_session"("ur_ingest_session_id")
  const sessionAgent = JSON.stringify({
    name: "docling-processor",
    version: "2.0.0",
    type: "document_extraction",
    library: "docling"
  });

  console.log(`INSERT INTO ur_ingest_session (ur_ingest_session_id, device_id, behavior_id, behavior_json, ingest_started_at, ingest_finished_at, session_agent, elaboration, created_by) VALUES ('${sessionId}', '${deviceId}', NULL, NULL, 'CURRENT_TIMESTAMP', NULL, '${sessionAgent}', NULL, '''UNKNOWN''');`);

  try {
    // Process files
    let fileCount = 0;
    for await (const entry of walk(testFixturesPath, {
      includeFiles: true,
      includeDirs: false,
      exts: [".md", ".txt", ".json", ".html"] // Supported text formats
    })) {
      const file = await processFile(entry.path);
      fileCount++;
      const resourceId = generateId();
      const transformId = generateId();

      const fileName = basename(file.uri);
      console.log(`-- Processing: ${fileName};`);

      // Insert uniform_resource with NULL content initially
      const elaboration = JSON.stringify({
        docling_processed: true,
        source_file: fileName,
        extraction_success: true,
        processor: "docling"
      });

      console.log(`INSERT INTO uniform_resource (uniform_resource_id, device_id, ingest_session_id, uri, content_digest, content, nature, size_bytes, last_modified_at, content_fm_body_attrs, frontmatter, elaboration, created_by) VALUES ('${resourceId}', '${deviceId}', '${sessionId}', '${file.uri}', '${file.hash}', NULL, '${file.nature}', ${file.size}, NULL, NULL, NULL, '${elaboration}', '''UNKNOWN''');`);

      console.log(`-- Binary content for ${file.uri} (${file.size} bytes) available;`);

      // Create transformation records
      console.log(createTransform(file, transformId, resourceId));
    }

    if (fileCount === 0) {
      console.log(`-- No processable files found in ${testFixturesPath}`);
    }

  } catch (error) {
    console.log(`-- Error processing files: ${error instanceof Error ? error.message : String(error)}`);
  }


  console.log("COMMIT;");
}

if (import.meta.main) {
  await main();
}