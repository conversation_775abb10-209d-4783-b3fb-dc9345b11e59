-- Running prerequisite: deno run -A poml-build.surveilr.ts
BEGIN;

-- Device and session setup
INSERT OR IGNORE INTO device (device_id, name, state, boundary) 
VALUES ('9FAF077CA63845DC9A5C3E789E', 'SIJU-JOSEPH-device', 'SINGLETON', 'UNKNOWN');

INSERT INTO ur_ingest_session (ur_ingest_session_id, device_id, behavior_id, behavior_json, ingest_started_at, ingest_finished_at) 
VALUES ('session-2025-09-12T10-17-19-994Z', '9FAF077CA63845DC9A5C3E789E', 'deno-processor', '{"name":"deno-document-processor","version":"1.0.0","type":"document_extraction","runtime":"deno"}', '2025-09-12T10:17:19.996Z', '2025-09-12T10:17:19.996Z');

COMMIT;
