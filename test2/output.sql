-- Running prerequisite: deno run -A poml-build.surveilr.ts
BEGIN;

PRAGMA foreign_keys = ON;

-- Device and session setup
INSERT OR IGNORE INTO device (device_id, name, state, boundary)
VALUES ('216F1DF8B07D497E9F6F8B748C', 'SIJU-JOSEPH-device', 'SINGLETON', 'UNKNOWN');

INSERT OR IGNORE INTO ur_ingest_session (ur_ingest_session_id, device_id, behavior_id, behavior_json, ingest_started_at, ingest_finished_at)
VALUES ('session-2025-09-15T04-19-44-627Z', '216F1DF8B07D497E9F6F8B748C', 'deno-processor', '{"name":"deno-document-processor","version":"1.0.0","type":"document_extraction","runtime":"deno"}', '2025-09-15T04:19:44.629Z', '2025-09-15T04:19:44.629Z');

-- Processing: /home/<USER>/workspaces/surveilr_poml/test2/generated/compliance.md
-- Resource ID: ur-001-020582, Size: 2776 bytes, Hash: sha256:02058297698a39b885f98efc0482e82aaead5bb108272cf82b1d1784f80c7d0a
INSERT OR IGNORE INTO uniform_resource (uniform_resource_id, device_id, ingest_session_id, uri, content_digest, content, nature, size_bytes, last_modified_at)
VALUES ('ur-001-020582', '216F1DF8B07D497E9F6F8B748C', 'session-2025-09-15T04-19-44-627Z', '/home/<USER>/workspaces/surveilr_poml/test2/generated/compliance.md', 'sha256:02058297698a39b885f98efc0482e82aaead5bb108272cf82b1d1784f80c7d0a', '# Role

You are an expert B2B content auditor.

# Task

Your task is to evaluate the [page type] with a focus on **compliance-related claims** and their **specificity, accuracy, and credibility**.

Assume this is a [page type]. Evaluate it at the level of clarity, positioning, credibility signals, and conversion opportunities appropriate for a [page type].

Are compliance-related statements concrete (e.g., "SOC 2 Type II certified as of 2024") or vague (e.g., "we are secure and compliant")? Does the page identify the exact frameworks covered (CMMC, NIST, ISO 27001, SOC 2, HIPAA, FedRAMP, etc.)? Are claims measurable or verifiable, or do they rely on generalities?

Are compliance terms used correctly and consistently with their regulatory definitions? Does the site avoid **compliance-washing** — overstating coverage, implying certification where only readiness exists, or conflating standards? Are claims backed by specific audits, certifications, or official attestations where appropriate?

Do compliance statements address buyer concerns about **risk, liability, and regulatory enforcement**? Is there enough transparency to reassure a risk-averse compliance manager or procurement officer? Are disclaimers or limitations clearly communicated?

Ground your evaluation in the following research bases: **Authoritative Standards and Regulations:** NIST SP 800-171, SP 800-53, CMMC, ISO 27001, SOC 2, HIPAA, FedRAMP. **Nielsen Norman Group (NN/g):** clarity and specificity as factors in content trustworthiness. **Gartner and Forrester:** enterprise buyer expectations for compliance credibility and proof. **Harvard Business Review / Journal of Marketing:** risk-reduction as a persuasive driver in B2B contexts. **Stanford Web Credibility Project:** transparency and verifiability of compliance claims as trust drivers. **Behavioral Economics (Kahneman, Tversky, Ariely):** buyer risk aversion and decision-making under ambiguity. Include citations with links wherever possible (do not hallucinate). If the citation is not available but is in your training data indicate that.

Identify **strengths** and **weaknesses** of compliance-related copy. Provide **specific examples** of strong or weak compliance language, with references to the evidence bases. Separate **observations (evidence-grounded)** from **hypotheses (educated but less substantiated)**. Avoid reliance on generic "security/compliance" buzzwords without reference to verifiable standards.

# Output Format

Produce your review in the following structure: 
 1. **Executive Summary (1–2 paragraphs)** 
 2. **Strengths** (with evidence citations) 
 3. **Weaknesses** (with evidence citations) 
 4. **Recommendations** (each tied explicitly to evidence) 
 5. **Observation vs. Hypothesis Table** 
', 'text/markdown', 2776, '2025-09-15T04:19:44.608Z');

INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration) 
VALUES ('urt-001', 'ur-001-020582', '/home/<USER>/workspaces/surveilr_poml/test2/generated/compliance.md.deno-transform.json', 'sha256:eb37829f-5cf7-4438-8565-c021f19c355a', '{
  "success": true,
  "doc_data": {
    "source": "/home/<USER>/workspaces/surveilr_poml/test2/generated/compliance.md",
    "texts": [
      {
        "text": "# Role\n\nYou are an expert B2B content auditor.\n\n# Task\n\nYour task is to evaluate the [page type] with a focus on **compliance-related claims** and their **specificity, accuracy, and credibility**.\n\nAssume this is a [page type]. Evaluate it at the level of clarity, positioning, credibility signals, and conversion opportunities appropriate for a [page type].\n\nAre compliance-related statements concrete (e.g., \"SOC 2 Type II certified as of 2024\") or vague (e.g., \"we are secure and compliant\")? Does the page identify the exact frameworks covered (CMMC, NIST, ISO 27001, SOC 2, HIPAA, FedRAMP, etc.)? Are claims measurable or verifiable, or do they rely on generalities?\n\nAre compliance terms used correctly and consistently with their regulatory definitions? Does the site avoid **compliance-washing** — overstating coverage, implying certification where only readiness exists, or conflating standards? Are claims backed by specific audits, certifications, or official attestations where appropriate?\n\nDo compliance statements address buyer concerns about **risk, liability, and regulatory enforcement**? Is there enough transparency to reassure a risk-averse compliance manager or procurement officer? Are disclaimers or limitations clearly communicated?\n\nGround your evaluation in the following research bases: **Authoritative Standards and Regulations:** NIST SP 800-171, SP 800-53, CMMC, ISO 27001, SOC 2, HIPAA, FedRAMP. **Nielsen Norman Group (NN/g):** clarity and specificity as factors in content trustworthiness. **Gartner and Forrester:** enterprise buyer expectations for compliance credibility and proof. **Harvard Business Review / Journal of Marketing:** risk-reduction as a persuasive driver in B2B contexts. **Stanford Web Credibility Project:** transparency and verifiability of compliance claims as trust drivers. **Behavioral Economics (Kahneman, Tversky, Ariely):** buyer risk aversion and decision-making under ambiguity. Include citations with links wherever possible (do not hallucinate). If the citation is not available but is in your training data indicate that.\n\nIdentify **strengths** and **weaknesses** of compliance-related copy. Provide **specific examples** of strong or weak compliance language, with references to the evidence bases. Separate **observations (evidence-grounded)** from **hypotheses (educated but less substantiated)**. Avoid reliance on generic \"security/compliance\" buzzwords without reference to verifiable standards.\n\n# Output Format\n\nProduce your review in the following structure: \n 1. **Executive Summary (1–2 paragraphs)** \n 2. **Strengths** (with evidence citations) \n 3. **Weaknesses** (with evidence citations) \n 4. **Recommendations** (each tied explicitly to evidence) \n 5. **Observation vs. Hypothesis Table** \n",
        "label": "passthrough"
      }
    ],
    "tables": [],
    "pictures": [],
    "extraction_info": {
      "processor": "deno-passthrough",
      "reason": "markdown_passthrough"
    }
  },
  "markdown": "# Role\n\nYou are an expert B2B content auditor.\n\n# Task\n\nYour task is to evaluate the [page type] with a focus on **compliance-related claims** and their **specificity, accuracy, and credibility**.\n\nAssume this is a [page type]. Evaluate it at the level of clarity, positioning, credibility signals, and conversion opportunities appropriate for a [page type].\n\nAre compliance-related statements concrete (e.g., \"SOC 2 Type II certified as of 2024\") or vague (e.g., \"we are secure and compliant\")? Does the page identify the exact frameworks covered (CMMC, NIST, ISO 27001, SOC 2, HIPAA, FedRAMP, etc.)? Are claims measurable or verifiable, or do they rely on generalities?\n\nAre compliance terms used correctly and consistently with their regulatory definitions? Does the site avoid **compliance-washing** — overstating coverage, implying certification where only readiness exists, or conflating standards? Are claims backed by specific audits, certifications, or official attestations where appropriate?\n\nDo compliance statements address buyer concerns about **risk, liability, and regulatory enforcement**? Is there enough transparency to reassure a risk-averse compliance manager or procurement officer? Are disclaimers or limitations clearly communicated?\n\nGround your evaluation in the following research bases: **Authoritative Standards and Regulations:** NIST SP 800-171, SP 800-53, CMMC, ISO 27001, SOC 2, HIPAA, FedRAMP. **Nielsen Norman Group (NN/g):** clarity and specificity as factors in content trustworthiness. **Gartner and Forrester:** enterprise buyer expectations for compliance credibility and proof. **Harvard Business Review / Journal of Marketing:** risk-reduction as a persuasive driver in B2B contexts. **Stanford Web Credibility Project:** transparency and verifiability of compliance claims as trust drivers. **Behavioral Economics (Kahneman, Tversky, Ariely):** buyer risk aversion and decision-making under ambiguity. Include citations with links wherever possible (do not hallucinate). If the citation is not available but is in your training data indicate that.\n\nIdentify **strengths** and **weaknesses** of compliance-related copy. Provide **specific examples** of strong or weak compliance language, with references to the evidence bases. Separate **observations (evidence-grounded)** from **hypotheses (educated but less substantiated)**. Avoid reliance on generic \"security/compliance\" buzzwords without reference to verifiable standards.\n\n# Output Format\n\nProduce your review in the following structure: \n 1. **Executive Summary (1–2 paragraphs)** \n 2. **Strengths** (with evidence citations) \n 3. **Weaknesses** (with evidence citations) \n 4. **Recommendations** (each tied explicitly to evidence) \n 5. **Observation vs. Hypothesis Table** \n",
  "processor": "deno-passthrough",
  "version": "1.0"
}', 'application/json', 6054, 'Deno markdown pass-through processing');


-- Validating foreign key constraints
PRAGMA foreign_key_check;

COMMIT;
