-- Found 4 files to process;
BEGIN;
INSERT INTO ur_ingest_session (ur_ingest_session_id, device_id, behavior_id, behavior_json, ingest_started_at, ingest_finished_at, session_agent, elaboration, created_by) VALUES ('01K4YG0WNA0K678BNEDJ1JHHWA', '01K3HP14NN7H947E2WZ92G0QZS', NULL, NULL, 'CURRENT_TIMESTAMP', NULL, '{"name": "docling-processor", "version": "2.0.0", "type": "document_extraction", "library": "docling"}', NULL, '''UNKNOWN''');
-- Processing: README.md;
INSERT INTO uniform_resource (uniform_resource_id, device_id, ingest_session_id, uri, content_digest, content, nature, size_bytes, last_modified_at, content_fm_body_attrs, frontmatter, elaboration, created_by) VALUES ('01K4YG12KFY12VNAYQJG8533GV', '01K3HP14NN7H947E2WZ92G0QZS', '01K4YG0WNA0K678BNEDJ1JHHWA', '/home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/README.md', 'd66b74e5b08bae220e20fe2adcc8a3906bfa66fc4552f8dde218e90475f37e68', NULL, 'text/markdown', 174, NULL, NULL, NULL, '{"docling_processed": true, "source_file": "README.md", "extraction_success": true, "processor": "docling"}', '''UNKNOWN''');
-- Binary content for /home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/README.md (174 bytes) available;
INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration, created_by) VALUES ('01K4YG12KGV4X8MSS1ZTDFJMZA', '01K4YG12KFY12VNAYQJG8533GV', '/home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/README.md.docling.json', '29e5600e6dd29bfc0b38fff3e0ce4490e4ef6e0cf6a2fc6ec4af29193c948f0e', NULL, 'application/json', 3501, '{"transform_type": "docling_structured_data", "processor": "docling", "success": true}', '''UNKNOWN''');
UPDATE "uniform_resource_transform" SET "content" = '{
  "schema_name": "DoclingDocument",
  "version": "1.7.0",
  "name": "README",
  "origin": {
    "mimetype": "text/markdown",
    "binary_hash": 16292027857380802152,
    "filename": "README.md",
    "uri": null
  },
  "furniture": {
    "self_ref": "#/furniture",
    "parent": null,
    "children": [],
    "content_layer": "furniture",
    "name": "_root_",
    "label": "unspecified"
  },
  "body": {
    "self_ref": "#/body",
    "parent": null,
    "children": [
      {
        "cref": "#/texts/0"
      },
      {
        "cref": "#/groups/0"
      }
    ],
    "content_layer": "body",
    "name": "_root_",
    "label": "unspecified"
  },
  "groups": [
    {
      "self_ref": "#/groups/0",
      "parent": {
        "cref": "#/body"
      },
      "children": [
        {
          "cref": "#/texts/1"
        },
        {
          "cref": "#/texts/2"
        },
        {
          "cref": "#/texts/3"
        },
        {
          "cref": "#/texts/4"
        },
        {
          "cref": "#/texts/5"
        }
      ],
      "content_layer": "body",
      "name": "group",
      "label": "inline"
    }
  ],
  "texts": [
    {
      "self_ref": "#/texts/0",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "title",
      "prov": [],
      "orig": "Resource Surveillance Test Fixtures",
      "text": "Resource Surveillance Test Fixtures",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/1",
      "parent": {
        "cref": "#/groups/0"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "This directory contains",
      "text": "This directory contains",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/2",
      "parent": {
        "cref": "#/groups/0"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "test fixtures",
      "text": "test fixtures",
      "formatting": {
        "bold": false,
        "italic": true,
        "underline": false,
        "strikethrough": false,
        "script": "baseline"
      },
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/3",
      "parent": {
        "cref": "#/groups/0"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "(synthetic files) that can be used by",
      "text": "(synthetic files) that can be used by",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/4",
      "parent": {
        "cref": "#/groups/0"
      },
      "children": [],
      "content_layer": "body",
      "label": "code",
      "prov": [],
      "orig": "ingest",
      "text": "ingest",
      "formatting": null,
      "hyperlink": null,
      "captions": [],
      "references": [],
      "footnotes": [],
      "image": null,
      "code_language": "unknown"
    },
    {
      "self_ref": "#/texts/5",
      "parent": {
        "cref": "#/groups/0"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "and similar commands during end-to-end testing.",
      "text": "and similar commands during end-to-end testing.",
      "formatting": null,
      "hyperlink": null
    }
  ],
  "pictures": [],
  "tables": [],
  "key_value_items": [],
  "form_items": [],
  "pages": {}
}' WHERE "uniform_resource_transform_id" = '01K4YG12KGV4X8MSS1ZTDFJMZA';
INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration, created_by) VALUES ('01K4YG12KHX3VYYGNSMXWX0EST', '01K4YG12KFY12VNAYQJG8533GV', '/home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/README.md.docling.md', '0e4acf1cca6cf61d72c2501bb87009e46c8c4a4971b48f3b4c8ba0b73d89cbef', NULL, 'text/markdown', 173, '{"transform_type": "docling_markdown", "processor": "docling", "success": true}', '''UNKNOWN''');
UPDATE "uniform_resource_transform" SET "content" = '# Resource Surveillance Test Fixtures

This directory contains *test fixtures* (synthetic files) that can be used by `ingest` and similar commands during end-to-end testing.' WHERE "uniform_resource_transform_id" = '01K4YG12KHX3VYYGNSMXWX0EST';
-- Processing: markdown-with-frontmatter.md;
INSERT INTO uniform_resource (uniform_resource_id, device_id, ingest_session_id, uri, content_digest, content, nature, size_bytes, last_modified_at, content_fm_body_attrs, frontmatter, elaboration, created_by) VALUES ('01K4YG12M68Y96A5WKN8T76V56', '01K3HP14NN7H947E2WZ92G0QZS', '01K4YG0WNA0K678BNEDJ1JHHWA', '/home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/markdown-with-frontmatter.md', 'fa4c198dd08bcbc38e80148a31d7746f663d1f8c4dd9784baa003171e25432f0', NULL, 'text/markdown', 141, NULL, NULL, NULL, '{"docling_processed": true, "source_file": "markdown-with-frontmatter.md", "extraction_success": true, "processor": "docling"}', '''UNKNOWN''');
-- Binary content for /home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/markdown-with-frontmatter.md (141 bytes) available;
INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration, created_by) VALUES ('01K4YG12M7GHTKW34YCHKV2JW3', '01K4YG12M68Y96A5WKN8T76V56', '/home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/markdown-with-frontmatter.md.docling.json', '0396b7321cf33177d4888cd2c97179e23ffe4ad251ba342b9d9e7f4c9815cac7', NULL, 'application/json', 5432, '{"transform_type": "docling_structured_data", "processor": "docling", "success": true}', '''UNKNOWN''');
UPDATE "uniform_resource_transform" SET "content" = '{
  "schema_name": "DoclingDocument",
  "version": "1.7.0",
  "name": "markdown-with-frontmatter",
  "origin": {
    "mimetype": "text/markdown",
    "binary_hash": 12249845351645983472,
    "filename": "markdown-with-frontmatter.md",
    "uri": null
  },
  "furniture": {
    "self_ref": "#/furniture",
    "parent": null,
    "children": [],
    "content_layer": "furniture",
    "name": "_root_",
    "label": "unspecified"
  },
  "body": {
    "self_ref": "#/body",
    "parent": null,
    "children": [
      {
        "cref": "#/groups/0"
      },
      {
        "cref": "#/groups/1"
      },
      {
        "cref": "#/groups/2"
      }
    ],
    "content_layer": "body",
    "name": "_root_",
    "label": "unspecified"
  },
  "groups": [
    {
      "self_ref": "#/groups/0",
      "parent": {
        "cref": "#/body"
      },
      "children": [
        {
          "cref": "#/texts/0"
        },
        {
          "cref": "#/texts/1"
        }
      ],
      "content_layer": "body",
      "name": "group",
      "label": "inline"
    },
    {
      "self_ref": "#/groups/1",
      "parent": {
        "cref": "#/body"
      },
      "children": [
        {
          "cref": "#/texts/2"
        },
        {
          "cref": "#/texts/3"
        },
        {
          "cref": "#/texts/4"
        }
      ],
      "content_layer": "body",
      "name": "list",
      "label": "list"
    },
    {
      "self_ref": "#/groups/2",
      "parent": {
        "cref": "#/body"
      },
      "children": [
        {
          "cref": "#/texts/5"
        },
        {
          "cref": "#/texts/6"
        },
        {
          "cref": "#/texts/7"
        },
        {
          "cref": "#/texts/8"
        },
        {
          "cref": "#/texts/9"
        }
      ],
      "content_layer": "body",
      "name": "group",
      "label": "inline"
    }
  ],
  "texts": [
    {
      "self_ref": "#/texts/0",
      "parent": {
        "cref": "#/groups/0"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "title: \"Markdown with YAML Frontmatter Fixture\"",
      "text": "title: \"Markdown with YAML Frontmatter Fixture\"",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/1",
      "parent": {
        "cref": "#/groups/0"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "tags:",
      "text": "tags:",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/2",
      "parent": {
        "cref": "#/groups/1"
      },
      "children": [],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "tag1",
      "text": "tag1",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/3",
      "parent": {
        "cref": "#/groups/1"
      },
      "children": [],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "tag2",
      "text": "tag2",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/4",
      "parent": {
        "cref": "#/groups/1"
      },
      "children": [],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "tag3",
      "text": "tag3",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/5",
      "parent": {
        "cref": "#/groups/2"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "This is a",
      "text": "This is a",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/6",
      "parent": {
        "cref": "#/groups/2"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "test",
      "text": "test",
      "formatting": {
        "bold": true,
        "italic": false,
        "underline": false,
        "strikethrough": false,
        "script": "baseline"
      },
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/7",
      "parent": {
        "cref": "#/groups/2"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "of",
      "text": "of",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/8",
      "parent": {
        "cref": "#/groups/2"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "Markdown with Frontmatter",
      "text": "Markdown with Frontmatter",
      "formatting": {
        "bold": false,
        "italic": true,
        "underline": false,
        "strikethrough": false,
        "script": "baseline"
      },
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/9",
      "parent": {
        "cref": "#/groups/2"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": ".",
      "text": ".",
      "formatting": null,
      "hyperlink": null
    }
  ],
  "pictures": [],
  "tables": [],
  "key_value_items": [],
  "form_items": [],
  "pages": {}
}' WHERE "uniform_resource_transform_id" = '01K4YG12M7GHTKW34YCHKV2JW3';
INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration, created_by) VALUES ('01K4YG12M7GHTKW34YCHKV2JW4', '01K4YG12M68Y96A5WKN8T76V56', '/home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/markdown-with-frontmatter.md.docling.md', '1eccb2ce2d16c1f37bf073e74b95538bcfd04d8fb08b938536538e1838319bcd', NULL, 'text/markdown', 128, '{"transform_type": "docling_markdown", "processor": "docling", "success": true}', '''UNKNOWN''');
UPDATE "uniform_resource_transform" SET "content" = 'title: "Markdown with YAML Frontmatter Fixture" tags:

- tag1
- tag2
- tag3

This is a **test** of *Markdown with Frontmatter* .' WHERE "uniform_resource_transform_id" = '01K4YG12M7GHTKW34YCHKV2JW4';
-- Processing: plain-text.txt;
INSERT INTO uniform_resource (uniform_resource_id, device_id, ingest_session_id, uri, content_digest, content, nature, size_bytes, last_modified_at, content_fm_body_attrs, frontmatter, elaboration, created_by) VALUES ('01K4YG12MFC5GCFCDMDAVCCHBW', '01K3HP14NN7H947E2WZ92G0QZS', '01K4YG0WNA0K678BNEDJ1JHHWA', '/home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/plain-text.txt', 'c0529283ea3c0ee144fb31fe4e77f088453fab0cd588c63de9fe330aee8b5718', NULL, 'text/plain', 26, NULL, NULL, NULL, '{"docling_processed": true, "source_file": "plain-text.txt", "extraction_success": false, "processor": "fallback"}', '''UNKNOWN''');
-- Binary content for /home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/plain-text.txt (26 bytes) available;
INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration, created_by) VALUES ('01K4YG12MG0VZ0BY3WKVFVKFKB', '01K4YG12MFC5GCFCDMDAVCCHBW', '/home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/plain-text.txt.docling.json', 'eaca3c1820dd6e3820f50bce4a242ff0738bc0d98b494ece40f2b83ad3b9bc67', NULL, 'application/json', 354, '{"transform_type": "docling_structured_data", "processor": "fallback", "success": false}', '''UNKNOWN''');
UPDATE "uniform_resource_transform" SET "content" = '{
  "source": "../../assurance/test-fixtures/plain-text.txt",
  "texts": [
    {
      "text": "this is plain text content",
      "label": "fallback"
    }
  ],
  "tables": [],
  "pictures": [],
  "extraction_info": {
    "processor": "fallback",
    "reason": "docling_error: File format not allowed: ../../assurance/test-fixtures/plain-text.txt"
  }
}' WHERE "uniform_resource_transform_id" = '01K4YG12MG0VZ0BY3WKVFVKFKB';
INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration, created_by) VALUES ('01K4YG12MG0VZ0BY3WKVFVKFKC', '01K4YG12MFC5GCFCDMDAVCCHBW', '/home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/plain-text.txt.docling.md', 'fb0e6ac80ebf0ac373db9eb61f4d561c5d6f17f0f569ec3e2596996a1eb0a188', NULL, 'text/markdown', 44, '{"transform_type": "docling_markdown", "processor": "fallback", "success": false}', '''UNKNOWN''');
UPDATE "uniform_resource_transform" SET "content" = '# plain-text.txt

this is plain text content' WHERE "uniform_resource_transform_id" = '01K4YG12MG0VZ0BY3WKVFVKFKC';
-- Processing: test-mdq-selectors-comprehensive.md;
INSERT INTO uniform_resource (uniform_resource_id, device_id, ingest_session_id, uri, content_digest, content, nature, size_bytes, last_modified_at, content_fm_body_attrs, frontmatter, elaboration, created_by) VALUES ('01K4YG12QJ1XP006BATKX6QHJ4', '01K3HP14NN7H947E2WZ92G0QZS', '01K4YG0WNA0K678BNEDJ1JHHWA', '/home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/test-mdq-selectors-comprehensive.md', 'b5ea611a6b0c85f6918ce804d432326dcecb6c34d4f6a2460878b1da1b5301e7', NULL, 'text/markdown', 2075, NULL, NULL, NULL, '{"docling_processed": true, "source_file": "test-mdq-selectors-comprehensive.md", "extraction_success": true, "processor": "docling"}', '''UNKNOWN''');
-- Binary content for /home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/test-mdq-selectors-comprehensive.md (2075 bytes) available;
INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration, created_by) VALUES ('01K4YG12QKKHM1108FXCBPTQXX', '01K4YG12QJ1XP006BATKX6QHJ4', '/home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/test-mdq-selectors-comprehensive.md.docling.json', '7dc1e6d44bb49df71601895ec506714162d2ff3d5499d8e151d0796144fba11d', NULL, 'application/json', 54927, '{"transform_type": "docling_structured_data", "processor": "docling", "success": true}', '''UNKNOWN''');
UPDATE "uniform_resource_transform" SET "content" = '{
  "schema_name": "DoclingDocument",
  "version": "1.7.0",
  "name": "test-mdq-selectors-comprehensive",
  "origin": {
    "mimetype": "text/markdown",
    "binary_hash": 610433299828113895,
    "filename": "test-mdq-selectors-comprehensive.md",
    "uri": null
  },
  "furniture": {
    "self_ref": "#/furniture",
    "parent": null,
    "children": [],
    "content_layer": "furniture",
    "name": "_root_",
    "label": "unspecified"
  },
  "body": {
    "self_ref": "#/body",
    "parent": null,
    "children": [
      {
        "cref": "#/texts/0"
      },
      {
        "cref": "#/texts/1"
      },
      {
        "cref": "#/texts/2"
      },
      {
        "cref": "#/texts/3"
      },
      {
        "cref": "#/texts/4"
      },
      {
        "cref": "#/texts/5"
      },
      {
        "cref": "#/texts/6"
      },
      {
        "cref": "#/texts/7"
      },
      {
        "cref": "#/texts/8"
      },
      {
        "cref": "#/texts/9"
      },
      {
        "cref": "#/texts/10"
      },
      {
        "cref": "#/texts/11"
      },
      {
        "cref": "#/groups/0"
      },
      {
        "cref": "#/texts/15"
      },
      {
        "cref": "#/groups/1"
      },
      {
        "cref": "#/texts/19"
      },
      {
        "cref": "#/groups/2"
      },
      {
        "cref": "#/texts/24"
      },
      {
        "cref": "#/texts/25"
      },
      {
        "cref": "#/groups/3"
      },
      {
        "cref": "#/texts/38"
      },
      {
        "cref": "#/texts/39"
      },
      {
        "cref": "#/texts/40"
      },
      {
        "cref": "#/groups/8"
      },
      {
        "cref": "#/groups/9"
      },
      {
        "cref": "#/texts/46"
      },
      {
        "cref": "#/texts/47"
      },
      {
        "cref": "#/texts/48"
      },
      {
        "cref": "#/texts/49"
      },
      {
        "cref": "#/texts/50"
      },
      {
        "cref": "#/groups/10"
      },
      {
        "cref": "#/tables/0"
      },
      {
        "cref": "#/groups/11"
      },
      {
        "cref": "#/tables/1"
      },
      {
        "cref": "#/texts/51"
      },
      {
        "cref": "#/texts/52"
      },
      {
        "cref": "#/texts/53"
      },
      {
        "cref": "#/texts/54"
      },
      {
        "cref": "#/texts/55"
      }
    ],
    "content_layer": "body",
    "name": "_root_",
    "label": "unspecified"
  },
  "groups": [
    {
      "self_ref": "#/groups/0",
      "parent": {
        "cref": "#/body"
      },
      "children": [
        {
          "cref": "#/texts/12"
        },
        {
          "cref": "#/texts/13"
        },
        {
          "cref": "#/texts/14"
        }
      ],
      "content_layer": "body",
      "name": "list",
      "label": "list"
    },
    {
      "self_ref": "#/groups/1",
      "parent": {
        "cref": "#/body"
      },
      "children": [
        {
          "cref": "#/texts/16"
        },
        {
          "cref": "#/texts/17"
        },
        {
          "cref": "#/texts/18"
        }
      ],
      "content_layer": "body",
      "name": "list",
      "label": "list"
    },
    {
      "self_ref": "#/groups/2",
      "parent": {
        "cref": "#/body"
      },
      "children": [
        {
          "cref": "#/texts/20"
        },
        {
          "cref": "#/texts/21"
        },
        {
          "cref": "#/texts/22"
        },
        {
          "cref": "#/texts/23"
        }
      ],
      "content_layer": "body",
      "name": "list",
      "label": "list"
    },
    {
      "self_ref": "#/groups/3",
      "parent": {
        "cref": "#/body"
      },
      "children": [
        {
          "cref": "#/texts/26"
        },
        {
          "cref": "#/texts/29"
        },
        {
          "cref": "#/texts/32"
        },
        {
          "cref": "#/texts/35"
        }
      ],
      "content_layer": "body",
      "name": "list",
      "label": "list"
    },
    {
      "self_ref": "#/groups/4",
      "parent": {
        "cref": "#/texts/26"
      },
      "children": [
        {
          "cref": "#/texts/27"
        },
        {
          "cref": "#/texts/28"
        }
      ],
      "content_layer": "body",
      "name": "group",
      "label": "inline"
    },
    {
      "self_ref": "#/groups/5",
      "parent": {
        "cref": "#/texts/29"
      },
      "children": [
        {
          "cref": "#/texts/30"
        },
        {
          "cref": "#/texts/31"
        }
      ],
      "content_layer": "body",
      "name": "group",
      "label": "inline"
    },
    {
      "self_ref": "#/groups/6",
      "parent": {
        "cref": "#/texts/32"
      },
      "children": [
        {
          "cref": "#/texts/33"
        },
        {
          "cref": "#/texts/34"
        }
      ],
      "content_layer": "body",
      "name": "group",
      "label": "inline"
    },
    {
      "self_ref": "#/groups/7",
      "parent": {
        "cref": "#/texts/35"
      },
      "children": [
        {
          "cref": "#/texts/36"
        },
        {
          "cref": "#/pictures/0"
        },
        {
          "cref": "#/texts/37"
        }
      ],
      "content_layer": "body",
      "name": "group",
      "label": "inline"
    },
    {
      "self_ref": "#/groups/8",
      "parent": {
        "cref": "#/body"
      },
      "children": [
        {
          "cref": "#/texts/41"
        },
        {
          "cref": "#/texts/42"
        }
      ],
      "content_layer": "body",
      "name": "list",
      "label": "list"
    },
    {
      "self_ref": "#/groups/9",
      "parent": {
        "cref": "#/body"
      },
      "children": [
        {
          "cref": "#/texts/43"
        },
        {
          "cref": "#/texts/44"
        },
        {
          "cref": "#/texts/45"
        }
      ],
      "content_layer": "body",
      "name": "group",
      "label": "inline"
    },
    {
      "self_ref": "#/groups/10",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "name": "group",
      "label": "inline"
    },
    {
      "self_ref": "#/groups/11",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "name": "group",
      "label": "inline"
    }
  ],
  "texts": [
    {
      "self_ref": "#/texts/0",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "title: \"Comprehensive mdq Selector Test File\"",
      "text": "title: \"Comprehensive mdq Selector Test File\"",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/1",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "description: \"Test file for all mdq selectors in Surveilr transform-markdown\"",
      "text": "description: \"Test file for all mdq selectors in Surveilr transform-markdown\"",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/2",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "title",
      "prov": [],
      "orig": "Alpha Section",
      "text": "Alpha Section",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/3",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "This is a paragraph in the Alpha section with some regular text content.",
      "text": "This is a paragraph in the Alpha section with some regular text content.",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/4",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "section_header",
      "prov": [],
      "orig": "Sub-section Header",
      "text": "Sub-section Header",
      "formatting": null,
      "hyperlink": null,
      "level": 1
    },
    {
      "self_ref": "#/texts/5",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "Another paragraph here with more detailed information.",
      "text": "Another paragraph here with more detailed information.",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/6",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "section_header",
      "prov": [],
      "orig": "Tertiary Header",
      "text": "Tertiary Header",
      "formatting": null,
      "hyperlink": null,
      "level": 2
    },
    {
      "self_ref": "#/texts/7",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "Content under the tertiary header.",
      "text": "Content under the tertiary header.",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/8",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "title",
      "prov": [],
      "orig": "Bravo Section",
      "text": "Bravo Section",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/9",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "This is the Bravo section content.",
      "text": "This is the Bravo section content.",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/10",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "section_header",
      "prov": [],
      "orig": "Lists Section",
      "text": "Lists Section",
      "formatting": null,
      "hyperlink": null,
      "level": 1
    },
    {
      "self_ref": "#/texts/11",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "section_header",
      "prov": [],
      "orig": "Unordered Lists",
      "text": "Unordered Lists",
      "formatting": null,
      "hyperlink": null,
      "level": 2
    },
    {
      "self_ref": "#/texts/12",
      "parent": {
        "cref": "#/groups/0"
      },
      "children": [],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "First unordered item",
      "text": "First unordered item",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/13",
      "parent": {
        "cref": "#/groups/0"
      },
      "children": [],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "Second unordered item",
      "text": "Second unordered item",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/14",
      "parent": {
        "cref": "#/groups/0"
      },
      "children": [],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "Third unordered item",
      "text": "Third unordered item",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/15",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "section_header",
      "prov": [],
      "orig": "Ordered Lists",
      "text": "Ordered Lists",
      "formatting": null,
      "hyperlink": null,
      "level": 2
    },
    {
      "self_ref": "#/texts/16",
      "parent": {
        "cref": "#/groups/1"
      },
      "children": [],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "First ordered item",
      "text": "First ordered item",
      "formatting": null,
      "hyperlink": null,
      "enumerated": true,
      "marker": ""
    },
    {
      "self_ref": "#/texts/17",
      "parent": {
        "cref": "#/groups/1"
      },
      "children": [],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "Second ordered item",
      "text": "Second ordered item",
      "formatting": null,
      "hyperlink": null,
      "enumerated": true,
      "marker": ""
    },
    {
      "self_ref": "#/texts/18",
      "parent": {
        "cref": "#/groups/1"
      },
      "children": [],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "Third ordered item",
      "text": "Third ordered item",
      "formatting": null,
      "hyperlink": null,
      "enumerated": true,
      "marker": ""
    },
    {
      "self_ref": "#/texts/19",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "section_header",
      "prov": [],
      "orig": "Task Lists",
      "text": "Task Lists",
      "formatting": null,
      "hyperlink": null,
      "level": 2
    },
    {
      "self_ref": "#/texts/20",
      "parent": {
        "cref": "#/groups/2"
      },
      "children": [],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "[ ] Incomplete task item",
      "text": "[ ] Incomplete task item",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/21",
      "parent": {
        "cref": "#/groups/2"
      },
      "children": [],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "[x] Completed task item",
      "text": "[x] Completed task item",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/22",
      "parent": {
        "cref": "#/groups/2"
      },
      "children": [],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "[ ] Another incomplete task",
      "text": "[ ] Another incomplete task",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/23",
      "parent": {
        "cref": "#/groups/2"
      },
      "children": [],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "[x] Another completed task",
      "text": "[x] Another completed task",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/24",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "section_header",
      "prov": [],
      "orig": "Links and References",
      "text": "Links and References",
      "formatting": null,
      "hyperlink": null,
      "level": 1
    },
    {
      "self_ref": "#/texts/25",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "Here are various links to test:",
      "text": "Here are various links to test:",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/26",
      "parent": {
        "cref": "#/groups/3"
      },
      "children": [
        {
          "cref": "#/groups/4"
        }
      ],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "",
      "text": "",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/27",
      "parent": {
        "cref": "#/groups/4"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "an inline",
      "text": "an inline",
      "formatting": null,
      "hyperlink": "https://example.com/"
    },
    {
      "self_ref": "#/texts/28",
      "parent": {
        "cref": "#/groups/4"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "link",
      "text": "link",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/29",
      "parent": {
        "cref": "#/groups/3"
      },
      "children": [
        {
          "cref": "#/groups/5"
        }
      ],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "",
      "text": "",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/30",
      "parent": {
        "cref": "#/groups/5"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "Reference link:",
      "text": "Reference link:",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/31",
      "parent": {
        "cref": "#/groups/5"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "referenced link",
      "text": "referenced link",
      "formatting": null,
      "hyperlink": "/referenced"
    },
    {
      "self_ref": "#/texts/32",
      "parent": {
        "cref": "#/groups/3"
      },
      "children": [
        {
          "cref": "#/groups/6"
        }
      ],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "",
      "text": "",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/33",
      "parent": {
        "cref": "#/groups/6"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "Another",
      "text": "Another",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/34",
      "parent": {
        "cref": "#/groups/6"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "inline link",
      "text": "inline link",
      "formatting": null,
      "hyperlink": "example.com"
    },
    {
      "self_ref": "#/texts/35",
      "parent": {
        "cref": "#/groups/3"
      },
      "children": [
        {
          "cref": "#/groups/7"
        }
      ],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "",
      "text": "",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/36",
      "parent": {
        "cref": "#/groups/7"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "Image:",
      "text": "Image:",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/37",
      "parent": {
        "cref": "#/groups/7"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "alt text",
      "text": "alt text",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/38",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "section_header",
      "prov": [],
      "orig": "Blockquotes",
      "text": "Blockquotes",
      "formatting": null,
      "hyperlink": null,
      "level": 1
    },
    {
      "self_ref": "#/texts/39",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "This is a single blockquote with some text.",
      "text": "This is a single blockquote with some text.",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/40",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "This is blockquote two with different content.",
      "text": "This is blockquote two with different content.",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/41",
      "parent": {
        "cref": "#/groups/8"
      },
      "children": [],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "This is a blockquote with a list item",
      "text": "This is a blockquote with a list item",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/42",
      "parent": {
        "cref": "#/groups/8"
      },
      "children": [],
      "content_layer": "body",
      "label": "list_item",
      "prov": [],
      "orig": "Another list item in blockquote",
      "text": "Another list item in blockquote",
      "formatting": null,
      "hyperlink": null,
      "enumerated": false,
      "marker": ""
    },
    {
      "self_ref": "#/texts/43",
      "parent": {
        "cref": "#/groups/9"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "Multi-line blockquote",
      "text": "Multi-line blockquote",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/44",
      "parent": {
        "cref": "#/groups/9"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "that spans several lines",
      "text": "that spans several lines",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/45",
      "parent": {
        "cref": "#/groups/9"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "with more content.",
      "text": "with more content.",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/46",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "section_header",
      "prov": [],
      "orig": "Paragraphs Section",
      "text": "Paragraphs Section",
      "formatting": null,
      "hyperlink": null,
      "level": 1
    },
    {
      "self_ref": "#/texts/47",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "This is a standalone paragraph for paragraph selection testing.",
      "text": "This is a standalone paragraph for paragraph selection testing.",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/48",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "Another paragraph with different content to test paragraph selectors.",
      "text": "Another paragraph with different content to test paragraph selectors.",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/49",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "A third paragraph with more text content.",
      "text": "A third paragraph with more text content.",
      "formatting": null,
      "hyperlink": null
    },
    {
      "self_ref": "#/texts/50",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "section_header",
      "prov": [],
      "orig": "Tables",
      "text": "Tables",
      "formatting": null,
      "hyperlink": null,
      "level": 1
    },
    {
      "self_ref": "#/texts/51",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "section_header",
      "prov": [],
      "orig": "Code Blocks",
      "text": "Code Blocks",
      "formatting": null,
      "hyperlink": null,
      "level": 1
    },
    {
      "self_ref": "#/texts/52",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "code",
      "prov": [],
      "orig": "function example() {\n    console.log(\"JavaScript code\");\n}",
      "text": "function example() {\n    console.log(\"JavaScript code\");\n}",
      "formatting": null,
      "hyperlink": null,
      "captions": [],
      "references": [],
      "footnotes": [],
      "image": null,
      "code_language": "unknown"
    },
    {
      "self_ref": "#/texts/53",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "code",
      "prov": [],
      "orig": "def python_example():\n    print(\"Python code\")",
      "text": "def python_example():\n    print(\"Python code\")",
      "formatting": null,
      "hyperlink": null,
      "captions": [],
      "references": [],
      "footnotes": [],
      "image": null,
      "code_language": "unknown"
    },
    {
      "self_ref": "#/texts/54",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "section_header",
      "prov": [],
      "orig": "Final Section",
      "text": "Final Section",
      "formatting": null,
      "hyperlink": null,
      "level": 1
    },
    {
      "self_ref": "#/texts/55",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "text",
      "prov": [],
      "orig": "This is the final section with concluding content for comprehensive testing.",
      "text": "This is the final section with concluding content for comprehensive testing.",
      "formatting": null,
      "hyperlink": null
    }
  ],
  "pictures": [
    {
      "self_ref": "#/pictures/0",
      "parent": {
        "cref": "#/groups/7"
      },
      "children": [],
      "content_layer": "body",
      "label": "picture",
      "prov": [],
      "captions": [],
      "references": [],
      "footnotes": [],
      "image": null,
      "annotations": []
    }
  ],
  "tables": [
    {
      "self_ref": "#/tables/0",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "table",
      "prov": [],
      "captions": [],
      "references": [],
      "footnotes": [],
      "image": null,
      "data": {
        "table_cells": [
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 0,
            "end_row_offset_idx": 1,
            "start_col_offset_idx": 0,
            "end_col_offset_idx": 1,
            "text": "Column A",
            "column_header": true,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 0,
            "end_row_offset_idx": 1,
            "start_col_offset_idx": 1,
            "end_col_offset_idx": 2,
            "text": "Column B",
            "column_header": true,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 0,
            "end_row_offset_idx": 1,
            "start_col_offset_idx": 2,
            "end_col_offset_idx": 3,
            "text": "Column C",
            "column_header": true,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 1,
            "end_row_offset_idx": 2,
            "start_col_offset_idx": 0,
            "end_col_offset_idx": 1,
            "text": "Alpha",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 1,
            "end_row_offset_idx": 2,
            "start_col_offset_idx": 1,
            "end_col_offset_idx": 2,
            "text": "Beta",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 1,
            "end_row_offset_idx": 2,
            "start_col_offset_idx": 2,
            "end_col_offset_idx": 3,
            "text": "Gamma",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 2,
            "end_row_offset_idx": 3,
            "start_col_offset_idx": 0,
            "end_col_offset_idx": 1,
            "text": "One",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 2,
            "end_row_offset_idx": 3,
            "start_col_offset_idx": 1,
            "end_col_offset_idx": 2,
            "text": "Two",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 2,
            "end_row_offset_idx": 3,
            "start_col_offset_idx": 2,
            "end_col_offset_idx": 3,
            "text": "Three",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 3,
            "end_row_offset_idx": 4,
            "start_col_offset_idx": 0,
            "end_col_offset_idx": 1,
            "text": "Red",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 3,
            "end_row_offset_idx": 4,
            "start_col_offset_idx": 1,
            "end_col_offset_idx": 2,
            "text": "Green",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 3,
            "end_row_offset_idx": 4,
            "start_col_offset_idx": 2,
            "end_col_offset_idx": 3,
            "text": "Blue",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 0,
            "end_row_offset_idx": 1,
            "start_col_offset_idx": 0,
            "end_col_offset_idx": 1,
            "text": "Column A",
            "column_header": true,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 0,
            "end_row_offset_idx": 1,
            "start_col_offset_idx": 1,
            "end_col_offset_idx": 2,
            "text": "Column B",
            "column_header": true,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 0,
            "end_row_offset_idx": 1,
            "start_col_offset_idx": 2,
            "end_col_offset_idx": 3,
            "text": "Column C",
            "column_header": true,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 1,
            "end_row_offset_idx": 2,
            "start_col_offset_idx": 0,
            "end_col_offset_idx": 1,
            "text": "Alpha",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 1,
            "end_row_offset_idx": 2,
            "start_col_offset_idx": 1,
            "end_col_offset_idx": 2,
            "text": "Beta",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 1,
            "end_row_offset_idx": 2,
            "start_col_offset_idx": 2,
            "end_col_offset_idx": 3,
            "text": "Gamma",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 2,
            "end_row_offset_idx": 3,
            "start_col_offset_idx": 0,
            "end_col_offset_idx": 1,
            "text": "One",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 2,
            "end_row_offset_idx": 3,
            "start_col_offset_idx": 1,
            "end_col_offset_idx": 2,
            "text": "Two",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 2,
            "end_row_offset_idx": 3,
            "start_col_offset_idx": 2,
            "end_col_offset_idx": 3,
            "text": "Three",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 3,
            "end_row_offset_idx": 4,
            "start_col_offset_idx": 0,
            "end_col_offset_idx": 1,
            "text": "Red",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 3,
            "end_row_offset_idx": 4,
            "start_col_offset_idx": 1,
            "end_col_offset_idx": 2,
            "text": "Green",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 3,
            "end_row_offset_idx": 4,
            "start_col_offset_idx": 2,
            "end_col_offset_idx": 3,
            "text": "Blue",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          }
        ],
        "num_rows": 4,
        "num_cols": 3,
        "grid": [
          [
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 0,
              "end_row_offset_idx": 1,
              "start_col_offset_idx": 0,
              "end_col_offset_idx": 1,
              "text": "Column A",
              "column_header": true,
              "row_header": false,
              "row_section": false,
              "fillable": false
            },
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 0,
              "end_row_offset_idx": 1,
              "start_col_offset_idx": 1,
              "end_col_offset_idx": 2,
              "text": "Column B",
              "column_header": true,
              "row_header": false,
              "row_section": false,
              "fillable": false
            },
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 0,
              "end_row_offset_idx": 1,
              "start_col_offset_idx": 2,
              "end_col_offset_idx": 3,
              "text": "Column C",
              "column_header": true,
              "row_header": false,
              "row_section": false,
              "fillable": false
            }
          ],
          [
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 1,
              "end_row_offset_idx": 2,
              "start_col_offset_idx": 0,
              "end_col_offset_idx": 1,
              "text": "Alpha",
              "column_header": false,
              "row_header": false,
              "row_section": false,
              "fillable": false
            },
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 1,
              "end_row_offset_idx": 2,
              "start_col_offset_idx": 1,
              "end_col_offset_idx": 2,
              "text": "Beta",
              "column_header": false,
              "row_header": false,
              "row_section": false,
              "fillable": false
            },
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 1,
              "end_row_offset_idx": 2,
              "start_col_offset_idx": 2,
              "end_col_offset_idx": 3,
              "text": "Gamma",
              "column_header": false,
              "row_header": false,
              "row_section": false,
              "fillable": false
            }
          ],
          [
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 2,
              "end_row_offset_idx": 3,
              "start_col_offset_idx": 0,
              "end_col_offset_idx": 1,
              "text": "One",
              "column_header": false,
              "row_header": false,
              "row_section": false,
              "fillable": false
            },
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 2,
              "end_row_offset_idx": 3,
              "start_col_offset_idx": 1,
              "end_col_offset_idx": 2,
              "text": "Two",
              "column_header": false,
              "row_header": false,
              "row_section": false,
              "fillable": false
            },
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 2,
              "end_row_offset_idx": 3,
              "start_col_offset_idx": 2,
              "end_col_offset_idx": 3,
              "text": "Three",
              "column_header": false,
              "row_header": false,
              "row_section": false,
              "fillable": false
            }
          ],
          [
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 3,
              "end_row_offset_idx": 4,
              "start_col_offset_idx": 0,
              "end_col_offset_idx": 1,
              "text": "Red",
              "column_header": false,
              "row_header": false,
              "row_section": false,
              "fillable": false
            },
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 3,
              "end_row_offset_idx": 4,
              "start_col_offset_idx": 1,
              "end_col_offset_idx": 2,
              "text": "Green",
              "column_header": false,
              "row_header": false,
              "row_section": false,
              "fillable": false
            },
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 3,
              "end_row_offset_idx": 4,
              "start_col_offset_idx": 2,
              "end_col_offset_idx": 3,
              "text": "Blue",
              "column_header": false,
              "row_header": false,
              "row_section": false,
              "fillable": false
            }
          ]
        ]
      },
      "annotations": []
    },
    {
      "self_ref": "#/tables/1",
      "parent": {
        "cref": "#/body"
      },
      "children": [],
      "content_layer": "body",
      "label": "table",
      "prov": [],
      "captions": [],
      "references": [],
      "footnotes": [],
      "image": null,
      "data": {
        "table_cells": [
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 0,
            "end_row_offset_idx": 1,
            "start_col_offset_idx": 0,
            "end_col_offset_idx": 1,
            "text": "Name",
            "column_header": true,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 0,
            "end_row_offset_idx": 1,
            "start_col_offset_idx": 1,
            "end_col_offset_idx": 2,
            "text": "Description",
            "column_header": true,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 0,
            "end_row_offset_idx": 1,
            "start_col_offset_idx": 2,
            "end_col_offset_idx": 3,
            "text": "Status",
            "column_header": true,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 1,
            "end_row_offset_idx": 2,
            "start_col_offset_idx": 0,
            "end_col_offset_idx": 1,
            "text": "Item 1",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 1,
            "end_row_offset_idx": 2,
            "start_col_offset_idx": 1,
            "end_col_offset_idx": 2,
            "text": "First item",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 1,
            "end_row_offset_idx": 2,
            "start_col_offset_idx": 2,
            "end_col_offset_idx": 3,
            "text": "Active",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 2,
            "end_row_offset_idx": 3,
            "start_col_offset_idx": 0,
            "end_col_offset_idx": 1,
            "text": "Item 2",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 2,
            "end_row_offset_idx": 3,
            "start_col_offset_idx": 1,
            "end_col_offset_idx": 2,
            "text": "Second item",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 2,
            "end_row_offset_idx": 3,
            "start_col_offset_idx": 2,
            "end_col_offset_idx": 3,
            "text": "Inactive",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 0,
            "end_row_offset_idx": 1,
            "start_col_offset_idx": 0,
            "end_col_offset_idx": 1,
            "text": "Name",
            "column_header": true,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 0,
            "end_row_offset_idx": 1,
            "start_col_offset_idx": 1,
            "end_col_offset_idx": 2,
            "text": "Description",
            "column_header": true,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 0,
            "end_row_offset_idx": 1,
            "start_col_offset_idx": 2,
            "end_col_offset_idx": 3,
            "text": "Status",
            "column_header": true,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 1,
            "end_row_offset_idx": 2,
            "start_col_offset_idx": 0,
            "end_col_offset_idx": 1,
            "text": "Item 1",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 1,
            "end_row_offset_idx": 2,
            "start_col_offset_idx": 1,
            "end_col_offset_idx": 2,
            "text": "First item",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 1,
            "end_row_offset_idx": 2,
            "start_col_offset_idx": 2,
            "end_col_offset_idx": 3,
            "text": "Active",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 2,
            "end_row_offset_idx": 3,
            "start_col_offset_idx": 0,
            "end_col_offset_idx": 1,
            "text": "Item 2",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 2,
            "end_row_offset_idx": 3,
            "start_col_offset_idx": 1,
            "end_col_offset_idx": 2,
            "text": "Second item",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          },
          {
            "bbox": null,
            "row_span": 1,
            "col_span": 1,
            "start_row_offset_idx": 2,
            "end_row_offset_idx": 3,
            "start_col_offset_idx": 2,
            "end_col_offset_idx": 3,
            "text": "Inactive",
            "column_header": false,
            "row_header": false,
            "row_section": false,
            "fillable": false
          }
        ],
        "num_rows": 3,
        "num_cols": 3,
        "grid": [
          [
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 0,
              "end_row_offset_idx": 1,
              "start_col_offset_idx": 0,
              "end_col_offset_idx": 1,
              "text": "Name",
              "column_header": true,
              "row_header": false,
              "row_section": false,
              "fillable": false
            },
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 0,
              "end_row_offset_idx": 1,
              "start_col_offset_idx": 1,
              "end_col_offset_idx": 2,
              "text": "Description",
              "column_header": true,
              "row_header": false,
              "row_section": false,
              "fillable": false
            },
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 0,
              "end_row_offset_idx": 1,
              "start_col_offset_idx": 2,
              "end_col_offset_idx": 3,
              "text": "Status",
              "column_header": true,
              "row_header": false,
              "row_section": false,
              "fillable": false
            }
          ],
          [
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 1,
              "end_row_offset_idx": 2,
              "start_col_offset_idx": 0,
              "end_col_offset_idx": 1,
              "text": "Item 1",
              "column_header": false,
              "row_header": false,
              "row_section": false,
              "fillable": false
            },
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 1,
              "end_row_offset_idx": 2,
              "start_col_offset_idx": 1,
              "end_col_offset_idx": 2,
              "text": "First item",
              "column_header": false,
              "row_header": false,
              "row_section": false,
              "fillable": false
            },
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 1,
              "end_row_offset_idx": 2,
              "start_col_offset_idx": 2,
              "end_col_offset_idx": 3,
              "text": "Active",
              "column_header": false,
              "row_header": false,
              "row_section": false,
              "fillable": false
            }
          ],
          [
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 2,
              "end_row_offset_idx": 3,
              "start_col_offset_idx": 0,
              "end_col_offset_idx": 1,
              "text": "Item 2",
              "column_header": false,
              "row_header": false,
              "row_section": false,
              "fillable": false
            },
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 2,
              "end_row_offset_idx": 3,
              "start_col_offset_idx": 1,
              "end_col_offset_idx": 2,
              "text": "Second item",
              "column_header": false,
              "row_header": false,
              "row_section": false,
              "fillable": false
            },
            {
              "bbox": null,
              "row_span": 1,
              "col_span": 1,
              "start_row_offset_idx": 2,
              "end_row_offset_idx": 3,
              "start_col_offset_idx": 2,
              "end_col_offset_idx": 3,
              "text": "Inactive",
              "column_header": false,
              "row_header": false,
              "row_section": false,
              "fillable": false
            }
          ]
        ]
      },
      "annotations": []
    }
  ],
  "key_value_items": [],
  "form_items": [],
  "pages": {}
}' WHERE "uniform_resource_transform_id" = '01K4YG12QKKHM1108FXCBPTQXX';
INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration, created_by) VALUES ('01K4YG12QM36QY23G64Y6V4RTT', '01K4YG12QJ1XP006BATKX6QHJ4', '/home/<USER>/workspaces/github.com/www.surveilr.com/lib/std/pysdk/../../assurance/test-fixtures/test-mdq-selectors-comprehensive.md.docling.md', 'e42bba4e872ed7a15aed002b1b50db9e1c5817d7b2e317d86df08d0a17bca7fe', NULL, 'text/markdown', 2008, '{"transform_type": "docling_markdown", "processor": "docling", "success": true}', '''UNKNOWN''');
UPDATE "uniform_resource_transform" SET "content" = 'title: "Comprehensive mdq Selector Test File"

description: "Test file for all mdq selectors in Surveilr transform-markdown"

# Alpha Section

This is a paragraph in the Alpha section with some regular text content.

## Sub-section Header

Another paragraph here with more detailed information.

### Tertiary Header

Content under the tertiary header.

# Bravo Section

This is the Bravo section content.

## Lists Section

### Unordered Lists

- First unordered item
- Second unordered item
- Third unordered item

### Ordered Lists

1. First ordered item
2. Second ordered item
3. Third ordered item

### Task Lists

- [ ] Incomplete task item
- [x] Completed task item
- [ ] Another incomplete task
- [x] Another completed task

## Links and References

Here are various links to test:

- [an inline](https://example.com/) link
- Reference link: [referenced link](/referenced)
- Another [inline link](example.com)
- Image: <!-- image --> alt text

## Blockquotes

This is a single blockquote with some text.

This is blockquote two with different content.

- This is a blockquote with a list item
- Another list item in blockquote

Multi-line blockquote that spans several lines with more content.

## Paragraphs Section

This is a standalone paragraph for paragraph selection testing.

Another paragraph with different content to test paragraph selectors.

A third paragraph with more text content.

## Tables

| Column A   | Column B   | Column C   |
|------------|------------|------------|
| Alpha      | Beta       | Gamma      |
| One        | Two        | Three      |
| Red        | Green      | Blue       |

| Name   | Description   | Status   |
|--------|---------------|----------|
| Item 1 | First item    | Active   |
| Item 2 | Second item   | Inactive |

## Code Blocks

```
function example() {
    console.log("JavaScript code");
}
```

```
def python_example():
    print("Python code")
```

## Final Section

This is the final section with concluding content for comprehensive testing.' WHERE "uniform_resource_transform_id" = '01K4YG12QM36QY23G64Y6V4RTT';
COMMIT;
