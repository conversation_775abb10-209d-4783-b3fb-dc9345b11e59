# `surveilr` Capturable Executables

Root Paths
- `.`

Capturable Executables RegExes
- `surveilr-SQL`

Capturable Executables Batched SQL RegExes
- `surveilr-SQL`

Ignore Entries
- `/(\.git|node_modules)/`

## ./test2/e2e-test.surveilr-SQL.ts

- Nature: `?nature`
- Batched SQL?: `true`
- `Exited(0)`


STDOUT
```?nature
-- Running prerequisite: deno run -A poml-build.surveilr.ts
-- Found 1 files to process;
BEGIN;
INSERT INTO ur_ingest_session (ur_ingest_session_id, device_id, behavior_id, behavior_json, ingest_started_at, ingest_finished_at, session_agent, elaboration, created_by) VALUES ('F0BA120DE12E4A54A5022AEA18', '01K3HP14NN7H947E2WZ92G0QZS', NULL, NULL, 'CURRENT_TIMESTAMP', NULL, '{"name":"docling-processor","version":"2.0.0","type":"document_extraction","library":"docling"}', NULL, '''UNKNOWN''');
-- Processing: compliance.md;
INSERT INTO uniform_resource (uniform_resource_id, device_id, ingest_session_id, uri, content_digest, content, nature, size_bytes, last_modified_at, content_fm_body_attrs, frontmatter, elaboration, created_by) VALUES ('412A78BFE7C14EA1A313E8A479', '01K3HP14NN7H947E2WZ92G0QZS', 'F0BA120DE12E4A54A5022AEA18', '/home/<USER>/workspaces/surveilr_poml/test2/generated/compliance.md', 'sha256:02058297698a39b885f98efc0482e82aaead5bb108272cf82b1d1784f80c7d0a', NULL, 'text/markdown', 2776, NULL, NULL, NULL, '{"docling_processed":true,"source_file":"compliance.md","extraction_success":true,"processor":"docling"}', '''UNKNOWN''');
-- Binary content for /home/<USER>/workspaces/surveilr_poml/test2/generated/compliance.md (2776 bytes) available;
INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration, created_by) VALUES ('AB562DA1AEEF4C0FAB3EC101C2', '412A78BFE7C14EA1A313E8A479', '/home/<USER>/workspaces/surveilr_poml/test2/generated/compliance.md.docling.json', '7a16de209bdb4b25a2c632d019025a8e', NULL, 'application/json', 6054, '{"transform_type":"docling_structured_data","processor":"docling","success":true}', '''UNKNOWN''');
UPDATE "uniform_resource_transform" SET "content" = '{
  "success": true,
  "doc_data": {
    "source": "/home/<USER>/workspaces/surveilr_poml/test2/generated/compliance.md",
    "texts": [
      {
        "text": "# Role\n\nYou are an expert B2B content auditor.\n\n# Task\n\nYour task is to evaluate the [page type] with a focus on **compliance-related claims** and their **specificity, accuracy, and credibility**.\n\nAssume this is a [page type]. Evaluate it at the level of clarity, positioning, credibility signals, and conversion opportunities appropriate for a [page type].\n\nAre compliance-related statements concrete (e.g., \"SOC 2 Type II certified as of 2024\") or vague (e.g., \"we are secure and compliant\")? Does the page identify the exact frameworks covered (CMMC, NIST, ISO 27001, SOC 2, HIPAA, FedRAMP, etc.)? Are claims measurable or verifiable, or do they rely on generalities?\n\nAre compliance terms used correctly and consistently with their regulatory definitions? Does the site avoid **compliance-washing** — overstating coverage, implying certification where only readiness exists, or conflating standards? Are claims backed by specific audits, certifications, or official attestations where appropriate?\n\nDo compliance statements address buyer concerns about **risk, liability, and regulatory enforcement**? Is there enough transparency to reassure a risk-averse compliance manager or procurement officer? Are disclaimers or limitations clearly communicated?\n\nGround your evaluation in the following research bases: **Authoritative Standards and Regulations:** NIST SP 800-171, SP 800-53, CMMC, ISO 27001, SOC 2, HIPAA, FedRAMP. **Nielsen Norman Group (NN/g):** clarity and specificity as factors in content trustworthiness. **Gartner and Forrester:** enterprise buyer expectations for compliance credibility and proof. **Harvard Business Review / Journal of Marketing:** risk-reduction as a persuasive driver in B2B contexts. **Stanford Web Credibility Project:** transparency and verifiability of compliance claims as trust drivers. **Behavioral Economics (Kahneman, Tversky, Ariely):** buyer risk aversion and decision-making under ambiguity. Include citations with links wherever possible (do not hallucinate). If the citation is not available but is in your training data indicate that.\n\nIdentify **strengths** and **weaknesses** of compliance-related copy. Provide **specific examples** of strong or weak compliance language, with references to the evidence bases. Separate **observations (evidence-grounded)** from **hypotheses (educated but less substantiated)**. Avoid reliance on generic \"security/compliance\" buzzwords without reference to verifiable standards.\n\n# Output Format\n\nProduce your review in the following structure: \n 1. **Executive Summary (1–2 paragraphs)** \n 2. **Strengths** (with evidence citations) \n 3. **Weaknesses** (with evidence citations) \n 4. **Recommendations** (each tied explicitly to evidence) \n 5. **Observation vs. Hypothesis Table** \n",
        "label": "passthrough"
      }
    ],
    "tables": [],
    "pictures": [],
    "extraction_info": {
      "processor": "deno-passthrough",
      "reason": "markdown_passthrough"
    }
  },
  "markdown": "# Role\n\nYou are an expert B2B content auditor.\n\n# Task\n\nYour task is to evaluate the [page type] with a focus on **compliance-related claims** and their **specificity, accuracy, and credibility**.\n\nAssume this is a [page type]. Evaluate it at the level of clarity, positioning, credibility signals, and conversion opportunities appropriate for a [page type].\n\nAre compliance-related statements concrete (e.g., \"SOC 2 Type II certified as of 2024\") or vague (e.g., \"we are secure and compliant\")? Does the page identify the exact frameworks covered (CMMC, NIST, ISO 27001, SOC 2, HIPAA, FedRAMP, etc.)? Are claims measurable or verifiable, or do they rely on generalities?\n\nAre compliance terms used correctly and consistently with their regulatory definitions? Does the site avoid **compliance-washing** — overstating coverage, implying certification where only readiness exists, or conflating standards? Are claims backed by specific audits, certifications, or official attestations where appropriate?\n\nDo compliance statements address buyer concerns about **risk, liability, and regulatory enforcement**? Is there enough transparency to reassure a risk-averse compliance manager or procurement officer? Are disclaimers or limitations clearly communicated?\n\nGround your evaluation in the following research bases: **Authoritative Standards and Regulations:** NIST SP 800-171, SP 800-53, CMMC, ISO 27001, SOC 2, HIPAA, FedRAMP. **Nielsen Norman Group (NN/g):** clarity and specificity as factors in content trustworthiness. **Gartner and Forrester:** enterprise buyer expectations for compliance credibility and proof. **Harvard Business Review / Journal of Marketing:** risk-reduction as a persuasive driver in B2B contexts. **Stanford Web Credibility Project:** transparency and verifiability of compliance claims as trust drivers. **Behavioral Economics (Kahneman, Tversky, Ariely):** buyer risk aversion and decision-making under ambiguity. Include citations with links wherever possible (do not hallucinate). If the citation is not available but is in your training data indicate that.\n\nIdentify **strengths** and **weaknesses** of compliance-related copy. Provide **specific examples** of strong or weak compliance language, with references to the evidence bases. Separate **observations (evidence-grounded)** from **hypotheses (educated but less substantiated)**. Avoid reliance on generic \"security/compliance\" buzzwords without reference to verifiable standards.\n\n# Output Format\n\nProduce your review in the following structure: \n 1. **Executive Summary (1–2 paragraphs)** \n 2. **Strengths** (with evidence citations) \n 3. **Weaknesses** (with evidence citations) \n 4. **Recommendations** (each tied explicitly to evidence) \n 5. **Observation vs. Hypothesis Table** \n",
  "processor": "deno-passthrough",
  "version": "1.0"
}' WHERE "uniform_resource_transform_id" = 'AB562DA1AEEF4C0FAB3EC101C2';
INSERT INTO uniform_resource_transform (uniform_resource_transform_id, uniform_resource_id, uri, content_digest, content, nature, size_bytes, elaboration, created_by) VALUES ('F5EBF69D6AB0456281CE36A1A5', '412A78BFE7C14EA1A313E8A479', '/home/<USER>/workspaces/surveilr_poml/test2/generated/compliance.md.docling.md', 'ffb3828c99ce48368b4e274e57f05d12', NULL, 'text/markdown', 2776, '{"transform_type":"docling_markdown","processor":"docling","success":true}', '''UNKNOWN''');
UPDATE "uniform_resource_transform" SET "content" = '# Role

You are an expert B2B content auditor.

# Task

Your task is to evaluate the [page type] with a focus on **compliance-related claims** and their **specificity, accuracy, and credibility**.

Assume this is a [page type]. Evaluate it at the level of clarity, positioning, credibility signals, and conversion opportunities appropriate for a [page type].

Are compliance-related statements concrete (e.g., "SOC 2 Type II certified as of 2024") or vague (e.g., "we are secure and compliant")? Does the page identify the exact frameworks covered (CMMC, NIST, ISO 27001, SOC 2, HIPAA, FedRAMP, etc.)? Are claims measurable or verifiable, or do they rely on generalities?

Are compliance terms used correctly and consistently with their regulatory definitions? Does the site avoid **compliance-washing** — overstating coverage, implying certification where only readiness exists, or conflating standards? Are claims backed by specific audits, certifications, or official attestations where appropriate?

Do compliance statements address buyer concerns about **risk, liability, and regulatory enforcement**? Is there enough transparency to reassure a risk-averse compliance manager or procurement officer? Are disclaimers or limitations clearly communicated?

Ground your evaluation in the following research bases: **Authoritative Standards and Regulations:** NIST SP 800-171, SP 800-53, CMMC, ISO 27001, SOC 2, HIPAA, FedRAMP. **Nielsen Norman Group (NN/g):** clarity and specificity as factors in content trustworthiness. **Gartner and Forrester:** enterprise buyer expectations for compliance credibility and proof. **Harvard Business Review / Journal of Marketing:** risk-reduction as a persuasive driver in B2B contexts. **Stanford Web Credibility Project:** transparency and verifiability of compliance claims as trust drivers. **Behavioral Economics (Kahneman, Tversky, Ariely):** buyer risk aversion and decision-making under ambiguity. Include citations with links wherever possible (do not hallucinate). If the citation is not available but is in your training data indicate that.

Identify **strengths** and **weaknesses** of compliance-related copy. Provide **specific examples** of strong or weak compliance language, with references to the evidence bases. Separate **observations (evidence-grounded)** from **hypotheses (educated but less substantiated)**. Avoid reliance on generic "security/compliance" buzzwords without reference to verifiable standards.

# Output Format

Produce your review in the following structure: 
 1. **Executive Summary (1–2 paragraphs)** 
 2. **Strengths** (with evidence citations) 
 3. **Weaknesses** (with evidence citations) 
 4. **Recommendations** (each tied explicitly to evidence) 
 5. **Observation vs. Hypothesis Table** 
' WHERE "uniform_resource_transform_id" = 'F5EBF69D6AB0456281CE36A1A5';
COMMIT;

```
> adfcd3bd59c273c7f547bc02807d6bd556fbfb06

Synthetic STDIN (for testing the execution)
```json
{"surveilr-ingest":{"args":{"state_db_fs_path":"synthetic"},"env":{"current_dir":"/home/<USER>/workspaces/surveilr_poml"},"behavior":{},"device":{"device_id":"synthetic"},"session":{"walk-session-id":"synthetic","walk-path-id":"synthetic","entry":{"path":"./test2/e2e-test.surveilr-SQL.ts"}}}}
```
## ./test/poml-build.surveilr[markdown].ts

- Nature: `markdown`
- Batched SQL?: `false`
- `Exited(1)`


STDOUT
```markdown

```
> da39a3ee5e6b4b0d3255bfef95601890afd80709

STDERR
```
[0m[1m[31merror[0m: unexpected argument '--allow-write
' found

  tip: a similar argument exists: '--allow-write'
  tip: to pass '--allow-write
' as a value, use '-- --allow-write
'

Usage: deno run [OPTIONS] [SCRIPT_ARG]...


```

Synthetic STDIN (for testing the execution)
```json
{"surveilr-ingest":{"args":{"state_db_fs_path":"synthetic"},"env":{"current_dir":"/home/<USER>/workspaces/surveilr_poml"},"behavior":{},"device":{"device_id":"synthetic"},"session":{"walk-session-id":"synthetic","walk-path-id":"synthetic","entry":{"path":"./test/poml-build.surveilr[markdown].ts"}}}}
```
## ./capturable-executable.surveilr[json].ts

- Nature: `json`
- Batched SQL?: `false`
- `Exited(0)`


STDOUT
```json
{
    "opsfolio-evidence": [
        {
            "fii": "xyz1",
            "producer": "cmd1",
            "evidence": "output from cmd1"
        },
        {
            "fii": "xyz2",
            "producer": "cmd2",
            "evidence": "output from cmd2"
        }
    ]
}

```
> 8ade03175686fefafdb346dfec5e25515d038dec

Synthetic STDIN (for testing the execution)
```json
{"surveilr-ingest":{"args":{"state_db_fs_path":"synthetic"},"env":{"current_dir":"/home/<USER>/workspaces/surveilr_poml"},"behavior":{},"device":{"device_id":"synthetic"},"session":{"walk-session-id":"synthetic","walk-path-id":"synthetic","entry":{"path":"./capturable-executable.surveilr[json].ts"}}}}
```

