{"version": 3, "file": "HTMLCollectionImpl.js", "sourceRoot": "", "sources": ["../../src/dom/HTMLCollectionImpl.ts"], "names": [], "mappings": ";;AACA,yCAA6D;AAC7D,0CAAwG;AACxG,gCAA+B;AAC/B,uCAAyC;AAEzC;;GAEG;AACH;IASE;;;;;OAKG;IACH,4BAAoB,IAAU,EAAE,MAAuC;QAbvE,UAAK,GAAY,IAAI,CAAA;QAcnB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QACjB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAA;QAErB,OAAO,IAAI,KAAK,CAAqB,IAAI,EAAE,IAAI,CAAC,CAAA;IAClD,CAAC;IAGD,sBAAI,sCAAM;QADV,kBAAkB;aAClB;YAAA,iBAcC;YAbC;;;eAGG;YACH,IAAI,KAAK,GAAG,CAAC,CAAA;YACb,IAAI,IAAI,GAAG,uCAA2B,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAC7D,UAAC,CAAC,IAAK,OAAA,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAzC,CAAyC,CAAC,CAAA;YACnD,OAAO,IAAI,KAAK,IAAI,EAAE;gBACpB,KAAK,EAAE,CAAA;gBACP,IAAI,GAAG,sCAA0B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAC9D,UAAC,CAAC,IAAK,OAAA,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAzC,CAAyC,CAAC,CAAA;aACpD;YACD,OAAO,KAAK,CAAA;QACd,CAAC;;;OAAA;IAED,kBAAkB;IAClB,iCAAI,GAAJ,UAAK,KAAa;QAAlB,iBAoBC;QAnBC;;;;WAIG;QACH,IAAI,CAAC,GAAG,CAAC,CAAA;QACT,IAAI,IAAI,GAAG,uCAA2B,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAC7D,UAAC,CAAC,IAAK,OAAA,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAzC,CAAyC,CAAmB,CAAA;QACrE,OAAO,IAAI,KAAK,IAAI,EAAE;YACpB,IAAI,CAAC,KAAK,KAAK;gBACb,OAAO,IAAI,CAAA;;gBAEX,CAAC,EAAE,CAAA;YAEL,IAAI,GAAG,sCAA0B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAC9D,UAAC,CAAC,IAAK,OAAA,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAzC,CAAyC,CAAmB,CAAA;SACtE;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,sCAAS,GAAT,UAAU,GAAW;QAArB,iBA+BC;QA9BC;;;;;;;WAOG;QACH,IAAI,GAAG,KAAK,EAAE;YAAE,OAAO,IAAI,CAAA;QAE3B,IAAI,GAAG,GAAG,uCAA2B,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAC5D,UAAC,CAAC,IAAK,OAAA,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAzC,CAAyC,CAAmB,CAAA;QAErE,OAAO,GAAG,IAAI,IAAI,EAAE;YAClB,IAAI,GAAG,CAAC,iBAAiB,KAAK,GAAG,EAAE;gBACjC,OAAO,GAAG,CAAA;aACX;iBAAM,IAAI,GAAG,CAAC,UAAU,KAAK,iBAAc,CAAC,IAAI,EAAE;gBACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAClD,IAAM,IAAI,GAAG,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;oBACnC,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI;wBACxD,IAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,KAAK,GAAG;wBACrD,OAAO,GAAG,CAAA;iBACb;aACF;YAED,GAAG,GAAG,sCAA0B,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAC5D,UAAC,CAAC,IAAK,OAAA,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,KAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAzC,CAAyC,CAAmB,CAAA;SACtE;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,6BAAC,MAAM,CAAC,QAAQ,CAAC,GAAjB;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAA;QACvB,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;QAC3B,IAAI,WAAW,GAAmB,uCAA2B,CAAC,IAAI,EAChE,KAAK,EAAE,KAAK,EAAE,UAAC,CAAC,IAAK,OAAA,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAnC,CAAmC,CAAmB,CAAA;QAE7E,OAAO;YACL,IAAI,EAAJ;gBACE,IAAI,WAAW,KAAK,IAAI,EAAE;oBACxB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;iBACnC;qBAAM;oBACL,IAAM,MAAM,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,CAAA;oBAClD,WAAW,GAAG,sCAA0B,CAAC,IAAI,EAAE,WAAW,EACxD,KAAK,EAAE,KAAK,EAAE,UAAC,CAAC,IAAK,OAAA,YAAK,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAnC,CAAmC,CAAmB,CAAA;oBAC7E,OAAO,MAAM,CAAA;iBACd;YACH,CAAC;SACF,CAAA;IACH,CAAC;IAQD;;OAEG;IACH,gCAAG,GAAH,UAAI,MAAsB,EAAE,GAAgB,EAAE,QAAa;QACzD,IAAI,CAAC,eAAQ,CAAC,GAAG,CAAC,IAAI,kBAAkB,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YAC1E,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAA;SAC1C;QAED,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;QACzB,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;YAChB,OAAO,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,SAAS,CAAA;SAC1C;aAAM;YACL,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,SAAS,CAAA;SACvC;IACH,CAAC;IAED;;OAEG;IACH,gCAAG,GAAH,UAAI,MAAsB,EAAE,GAAgB,EAAE,KAAc,EAAE,QAAa;QACzE,IAAI,CAAC,eAAQ,CAAC,GAAG,CAAC,IAAI,kBAAkB,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YAC1E,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;SACjD;QAED,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;QACzB,IAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YACzB,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,SAAS,CAAA;QAEtE,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;YACxB,4BAAgB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;YAC3C,OAAO,IAAI,CAAA;SACZ;aAAM;YACL,OAAO,KAAK,CAAA;SACb;IACH,CAAC;IAED;;;;;OAKG;IACI,0BAAO,GAAd,UAAe,IAAU,EACvB,MAAsD;QAAtD,uBAAA,EAAA,UAA2C,cAAM,OAAA,IAAI,EAAJ,CAAI,CAAC;QACtD,OAAO,IAAI,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;IAC7C,CAAC;IAlKgB,gCAAa,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ;QACrE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;IAkKtC,yBAAC;CAAA,AAzKD,IAyKC;AAzKY,gDAAkB"}