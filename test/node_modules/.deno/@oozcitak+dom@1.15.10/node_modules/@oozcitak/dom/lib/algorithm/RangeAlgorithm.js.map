{"version": 3, "file": "RangeAlgorithm.js", "sourceRoot": "", "sources": ["../../src/algorithm/RangeAlgorithm.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,gDAG0B;AAC1B,oDAE4B;AAC5B,gCAA+B;AAC/B,qDAAyE;AACzE,iDAGwB;AACxB,mEAAiE;AACjE,mEAAiG;AACjG,iDAA4C;AAC5C,yDAG4B;AAC5B,iDAA4C;AAE5C;;;;;GAKG;AACH,SAAgB,eAAe,CAAC,KAAoB;IAClD;;OAEG;IACH,OAAO,CAAC,KAAK,CAAC,UAAU,KAAK,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,UAAU,CAAC,CAAA;AACzF,CAAC;AALD,0CAKC;AAED;;;;GAIG;AACH,SAAgB,UAAU,CAAC,KAAoB;IAC7C;;OAEG;IACH,OAAO,6BAAa,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;AACxC,CAAC;AALD,gCAKC;AAED;;;;;GAKG;AACH,SAAgB,iBAAiB,CAAC,IAAU,EAAE,KAAoB;IAChE;;;;OAIG;IACH,OAAO,CAAC,6BAAa,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,KAAK,CAAC;QAC/C,+CAAsB,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,6BAAgB,CAAC,KAAK;QAC1E,+CAAsB,CAAC,CAAC,IAAI,EAAE,+BAAe,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,6BAAgB,CAAC,MAAM,CAAC,CAAA;AAClG,CAAC;AATD,8CASC;AAED;;;;;GAKG;AACH,SAAgB,0BAA0B,CAAC,IAAU,EAAE,KAAoB;IACzE;;;;OAIG;IACH,IAAM,UAAU,GAAG,iCAAiB,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IAClE,IAAM,QAAQ,GAAG,iCAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IAE9D,OAAO,CAAC,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,UAAU,IAAI,QAAQ,CAAC,CAAA;AAC/D,CAAC;AAVD,gEAUC;AAED;;;;;;GAMG;AACH,SAAgB,iBAAiB,CAAC,KAAoB,EAAE,IAAU,EAAE,MAAc;IAChF;;;;;;;;;OASG;IACH,IAAI,YAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE;QAClC,MAAM,IAAI,mCAAoB,EAAE,CAAA;KACjC;IACD,IAAI,MAAM,GAAG,+BAAe,CAAC,IAAI,CAAC,EAAE;QAClC,MAAM,IAAI,6BAAc,EAAE,CAAA;KAC3B;IAED,IAAM,EAAE,GAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;IAExC,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,6BAAa,CAAC,IAAI,CAAC;QAC3C,+CAAsB,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,6BAAgB,CAAC,KAAK,EAAE;QACnE,KAAK,CAAC,IAAI,GAAG,EAAE,CAAA;KAChB;IAED,KAAK,CAAC,MAAM,GAAG,EAAE,CAAA;AACnB,CAAC;AA1BD,8CA0BC;AAED;;;;;;GAMG;AACH,SAAgB,eAAe,CAAC,KAAoB,EAAE,IAAU,EAAE,MAAc;IAC9E;;;;;;;;;OASG;IACH,IAAI,YAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE;QAClC,MAAM,IAAI,mCAAoB,EAAE,CAAA;KACjC;IACD,IAAI,MAAM,GAAG,+BAAe,CAAC,IAAI,CAAC,EAAE;QAClC,MAAM,IAAI,6BAAc,EAAE,CAAA;KAC3B;IAED,IAAM,EAAE,GAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;IAExC,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,6BAAa,CAAC,IAAI,CAAC;QAC3C,+CAAsB,CAAC,EAAE,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,6BAAgB,CAAC,MAAM,EAAE;QACtE,KAAK,CAAC,MAAM,GAAG,EAAE,CAAA;KAClB;IAED,KAAK,CAAC,IAAI,GAAG,EAAE,CAAA;AACjB,CAAC;AA1BD,0CA0BC;AAED;;;;;GAKG;AACH,SAAgB,YAAY,CAAC,IAAU,EAAE,KAAoB;IAC3D;;;OAGG;IACH,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;IAC3B,IAAI,MAAM,KAAK,IAAI;QACjB,MAAM,IAAI,mCAAoB,EAAE,CAAA;IAElC;;;;OAIG;IACH,IAAM,KAAK,GAAG,0BAAU,CAAC,IAAI,CAAC,CAAA;IAC9B,KAAK,CAAC,MAAM,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IAC9B,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,CAAA;AAClC,CAAC;AAjBD,oCAiBC;AAED;;;;GAIG;AACH,SAAgB,aAAa,CAAC,KAAoB;;IAChD;;;;OAIG;IACH,IAAM,QAAQ,GAAG,yCAAuB,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,CAAA;IACxE,IAAI,eAAe,CAAC,KAAK,CAAC;QAAE,OAAO,QAAQ,CAAA;IAE3C;;;;OAIG;IACH,IAAM,iBAAiB,GAAG,KAAK,CAAC,UAAU,CAAA;IAC1C,IAAM,mBAAmB,GAAG,KAAK,CAAC,YAAY,CAAA;IAC9C,IAAM,eAAe,GAAG,KAAK,CAAC,QAAQ,CAAA;IACtC,IAAM,iBAAiB,GAAG,KAAK,CAAC,UAAU,CAAA;IAE1C;;;;;;;;;;;;OAYG;IACH,IAAI,iBAAiB,KAAK,eAAe;QACvC,YAAK,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,EAAE;QAC9C,IAAM,KAAK,GAAG,0BAAU,CAAC,iBAAiB,CAAkB,CAAA;QAC5D,KAAK,CAAC,KAAK,GAAG,oDAA2B,CACvC,iBAAiB,EAAE,mBAAmB,EACtC,iBAAiB,GAAG,mBAAmB,CAAC,CAAA;QAC1C,mCAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;QAChC,kDAAyB,CACvB,iBAAiB,EAAE,mBAAmB,EACtC,iBAAiB,GAAG,mBAAmB,EAAE,EAAE,CAAC,CAAA;QAC9C,OAAO,QAAQ,CAAA;KAChB;IAED;;;;OAIG;IACH,IAAI,cAAc,GAAG,iBAAiB,CAAA;IACtC,OAAO,CAAC,iCAAiB,CAAC,eAAe,EAAE,cAAc,EAAE,IAAI,CAAC,EAAE;QAChE,IAAI,cAAc,CAAC,OAAO,KAAK,IAAI,EAAE;YACnC,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAA;SACzC;QACD,cAAc,GAAG,cAAc,CAAC,OAAO,CAAA;KACxC;IAED;;;;;OAKG;IACH,IAAI,4BAA4B,GAAgB,IAAI,CAAA;IACpD,IAAI,CAAC,iCAAiB,CAAC,eAAe,EAAE,iBAAiB,EAAE,IAAI,CAAC,EAAE;;YAChE,KAAmB,IAAA,KAAA,SAAA,cAAc,CAAC,SAAS,CAAA,gBAAA,4BAAE;gBAAxC,IAAM,IAAI,WAAA;gBACb,IAAI,0BAA0B,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;oBAC3C,4BAA4B,GAAG,IAAI,CAAA;oBACnC,MAAK;iBACN;aACF;;;;;;;;;KACF;IAED;;;;;OAKG;IACH,IAAI,2BAA2B,GAAgB,IAAI,CAAA;IACnD,IAAI,CAAC,iCAAiB,CAAC,iBAAiB,EAAE,eAAe,EAAE,IAAI,CAAC,EAAE;QAChE,IAAM,QAAQ,YAAO,cAAc,CAAC,SAAS,CAAC,CAAA;QAC9C,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;YACxB,IAAI,0BAA0B,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;gBAC3C,2BAA2B,GAAG,IAAI,CAAA;gBAClC,MAAK;aACN;SACF;KACF;IAED;;;;;OAKG;IACH,IAAM,iBAAiB,GAAW,EAAE,CAAA;;QACpC,KAAoB,IAAA,KAAA,SAAA,cAAc,CAAC,SAAS,CAAA,gBAAA,4BAAE;YAAzC,IAAM,KAAK,WAAA;YACd,IAAI,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;gBACnC,IAAI,YAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE;oBACnC,MAAM,IAAI,oCAAqB,EAAE,CAAA;iBAClC;gBACD,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;aAC9B;SACF;;;;;;;;;IAED,IAAI,OAAa,CAAA;IACjB,IAAI,SAAiB,CAAA;IACrB,IAAI,iCAAiB,CAAC,eAAe,EAAE,iBAAiB,EAAE,IAAI,CAAC,EAAE;QAC/D;;;;WAIG;QACH,OAAO,GAAG,iBAAiB,CAAA;QAC3B,SAAS,GAAG,mBAAmB,CAAA;KAChC;SAAM;QACL;;;;;;;WAOG;QACH,IAAI,aAAa,GAAG,iBAAiB,CAAA;QACrC,OAAO,aAAa,CAAC,OAAO,KAAK,IAAI;YACnC,CAAC,iCAAiB,CAAC,eAAe,EAAE,aAAa,CAAC,OAAO,CAAC,EAAE;YAC5D,aAAa,GAAG,aAAa,CAAC,OAAO,CAAA;SACtC;QACD,0BAA0B;QAC1B,IAAI,aAAa,CAAC,OAAO,KAAK,IAAI,EAAE;YAClC;;;;eAIG;YACH,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;SACxC;QACD,OAAO,GAAG,aAAa,CAAC,OAAO,CAAA;QAC/B,SAAS,GAAG,CAAC,GAAG,0BAAU,CAAC,aAAa,CAAC,CAAA;KAC1C;IAED,IAAI,YAAK,CAAC,mBAAmB,CAAC,4BAA4B,CAAC,EAAE;QAC3D;;;;;;;;;;;WAWG;QACH,IAAM,KAAK,GAAG,0BAAU,CAAC,iBAAiB,CAAkB,CAAA;QAC5D,KAAK,CAAC,KAAK,GAAG,oDAA2B,CACvC,iBAAkC,EAAE,mBAAmB,EACvD,+BAAe,CAAC,iBAAiB,CAAC,GAAG,mBAAmB,CAAC,CAAA;QAC3D,mCAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;QAChC,kDAAyB,CAAC,iBAAkC,EAC1D,mBAAmB,EACnB,+BAAe,CAAC,iBAAiB,CAAC,GAAG,mBAAmB,EAAE,EAAE,CAAC,CAAA;KAChE;SAAM,IAAI,4BAA4B,KAAK,IAAI,EAAE;QAChD;;;;;;;;;WASG;QACH,IAAM,KAAK,GAAG,0BAAU,CAAC,4BAA4B,CAAC,CAAA;QACtD,mCAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;QAChC,IAAM,QAAQ,GAAG,8BAAY,CAC3B,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,EACxC,CAAC,4BAA4B,EAAE,+BAAe,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAA;QAChF,IAAM,WAAW,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAA;QAC3C,mCAAe,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;KACpC;;QAED;;;WAGG;QACH,KAAoB,IAAA,sBAAA,SAAA,iBAAiB,CAAA,oDAAA,mFAAE;YAAlC,IAAM,KAAK,8BAAA;YACd,mCAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;SACjC;;;;;;;;;IAED,IAAI,YAAK,CAAC,mBAAmB,CAAC,2BAA2B,CAAC,EAAE;QAC1D;;;;;;;;;WASG;QACH,IAAM,KAAK,GAAG,0BAAU,CAAC,eAAe,CAAkB,CAAA;QAC1D,KAAK,CAAC,KAAK,GAAG,oDAA2B,CACvC,eAAgC,EAAE,CAAC,EAAE,iBAAiB,CAAC,CAAA;QACzD,mCAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;QAChC,kDAAyB,CAAC,eAAgC,EACxD,CAAC,EAAE,iBAAiB,EAAE,EAAE,CAAC,CAAA;KAC5B;SAAM,IAAI,2BAA2B,KAAK,IAAI,EAAE;QAC/C;;;;;;;;;WASG;QACH,IAAM,KAAK,GAAG,0BAAU,CAAC,2BAA2B,CAAC,CAAA;QACrD,mCAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;QAChC,IAAM,QAAQ,GAAG,8BAAY,CAC3B,CAAC,2BAA2B,EAAE,CAAC,CAAC,EAChC,CAAC,eAAe,EAAE,iBAAiB,CAAC,CAAC,CAAA;QACvC,IAAM,WAAW,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAA;QAC3C,mCAAe,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;KACpC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;IACnC,KAAK,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;IAEjC;;OAEG;IACH,OAAO,QAAQ,CAAA;AACjB,CAAC;AAjPD,sCAiPC;AAED;;;;GAIG;AACH,SAAgB,sBAAsB,CAAC,KAAoB;;IACzD;;;;OAIG;IACH,IAAM,QAAQ,GAAG,yCAAuB,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,CAAA;IACxE,IAAI,eAAe,CAAC,KAAK,CAAC;QAAE,OAAO,QAAQ,CAAA;IAE3C;;;;;;;;;;;;OAYG;IACH,IAAM,iBAAiB,GAAG,KAAK,CAAC,UAAU,CAAA;IAC1C,IAAM,mBAAmB,GAAG,KAAK,CAAC,YAAY,CAAA;IAC9C,IAAM,eAAe,GAAG,KAAK,CAAC,QAAQ,CAAA;IACtC,IAAM,iBAAiB,GAAG,KAAK,CAAC,UAAU,CAAA;IAE1C,IAAI,iBAAiB,KAAK,eAAe;QACvC,YAAK,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,EAAE;QAC9C,IAAM,KAAK,GAAG,0BAAU,CAAC,iBAAiB,CAAkB,CAAA;QAC5D,KAAK,CAAC,KAAK,GAAG,oDAA2B,CACvC,iBAAiB,EAAE,mBAAmB,EACtC,iBAAiB,GAAG,mBAAmB,CAAC,CAAA;QAC1C,mCAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;KACjC;IAED;;;;OAIG;IACH,IAAI,cAAc,GAAG,iBAAiB,CAAA;IACtC,OAAO,CAAC,iCAAiB,CAAC,eAAe,EAAE,cAAc,EAAE,IAAI,CAAC,EAAE;QAChE,IAAI,cAAc,CAAC,OAAO,KAAK,IAAI,EAAE;YACnC,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAA;SACzC;QACD,cAAc,GAAG,cAAc,CAAC,OAAO,CAAA;KACxC;IAED;;;;;OAKG;IACH,IAAI,4BAA4B,GAAgB,IAAI,CAAA;IACpD,IAAI,CAAC,iCAAiB,CAAC,eAAe,EAAE,iBAAiB,EAAE,IAAI,CAAC,EAAE;;YAChE,KAAmB,IAAA,KAAA,SAAA,cAAc,CAAC,SAAS,CAAA,gBAAA,4BAAE;gBAAxC,IAAM,IAAI,WAAA;gBACb,IAAI,0BAA0B,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;oBAC3C,4BAA4B,GAAG,IAAI,CAAA;oBACnC,MAAK;iBACN;aACF;;;;;;;;;KACF;IAED;;;;;OAKG;IACH,IAAI,2BAA2B,GAAgB,IAAI,CAAA;IACnD,IAAI,CAAC,iCAAiB,CAAC,iBAAiB,EAAE,eAAe,EAAE,IAAI,CAAC,EAAE;QAChE,IAAM,QAAQ,YAAO,cAAc,CAAC,SAAS,CAAC,CAAA;QAC9C,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;YAC5C,IAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAA;YACxB,IAAI,0BAA0B,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;gBAC3C,2BAA2B,GAAG,IAAI,CAAA;gBAClC,MAAK;aACN;SACF;KACF;IAED;;;;;OAKG;IACH,IAAM,iBAAiB,GAAW,EAAE,CAAA;;QACpC,KAAoB,IAAA,KAAA,SAAA,cAAc,CAAC,SAAS,CAAA,gBAAA,4BAAE;YAAzC,IAAM,KAAK,WAAA;YACd,IAAI,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;gBACnC,IAAI,YAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE;oBACnC,MAAM,IAAI,oCAAqB,EAAE,CAAA;iBAClC;gBACD,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;aAC9B;SACF;;;;;;;;;IAED,IAAI,YAAK,CAAC,mBAAmB,CAAC,4BAA4B,CAAC,EAAE;QAC3D;;;;;;;;WAQG;QACH,IAAM,KAAK,GAAG,0BAAU,CAAC,iBAAiB,CAAkB,CAAA;QAC5D,KAAK,CAAC,KAAK,GAAG,oDAA2B,CACvC,iBAAkC,EAAE,mBAAmB,EACvD,+BAAe,CAAC,iBAAiB,CAAC,GAAG,mBAAmB,CAAC,CAAA;QAC3D,mCAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;KACjC;SAAM,IAAI,4BAA4B,KAAK,IAAI,EAAE;QAChD;;;;;;;;;;WAUG;QACH,IAAM,KAAK,GAAG,0BAAU,CAAC,4BAA4B,CAAC,CAAA;QACtD,mCAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;QAChC,IAAM,QAAQ,GAAG,8BAAY,CAC3B,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,EACxC,CAAC,4BAA4B,EAAE,+BAAe,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAA;QAChF,IAAM,WAAW,GAAG,sBAAsB,CAAC,QAAQ,CAAC,CAAA;QACpD,mCAAe,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;KACpC;;QAED;;;;;;WAMG;QACH,KAAoB,IAAA,sBAAA,SAAA,iBAAiB,CAAA,oDAAA,mFAAE;YAAlC,IAAM,KAAK,8BAAA;YACd,IAAM,KAAK,GAAG,0BAAU,CAAC,KAAK,CAAC,CAAA;YAC/B,mCAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;SACjC;;;;;;;;;IAED,IAAI,YAAK,CAAC,mBAAmB,CAAC,2BAA2B,CAAC,EAAE;QAC1D;;;;;;;WAOG;QACH,IAAM,KAAK,GAAG,0BAAU,CAAC,eAAe,CAAkB,CAAA;QAC1D,KAAK,CAAC,KAAK,GAAG,oDAA2B,CACvC,eAAgC,EAAE,CAAC,EAAE,iBAAiB,CAAC,CAAA;QACzD,mCAAe,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;KACjC;SAAM,IAAI,2BAA2B,KAAK,IAAI,EAAE;QAC/C;;;;;;;;;WASG;QACH,IAAM,KAAK,GAAG,0BAAU,CAAC,2BAA2B,CAAC,CAAA;QACrD,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QACtB,IAAM,QAAQ,GAAG,8BAAY,CAC3B,CAAC,2BAA2B,EAAE,CAAC,CAAC,EAChC,CAAC,eAAe,EAAE,iBAAiB,CAAC,CAAC,CAAA;QACvC,IAAM,WAAW,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAA;QAC3C,mCAAe,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;KACpC;IAED;;OAEG;IACH,OAAO,QAAQ,CAAA;AACjB,CAAC;AAxLD,wDAwLC;AAED;;;;;GAKG;AACH,SAAgB,YAAY,CAAC,IAAU,EAAE,KAAoB;;IAC3D;;;;OAIG;IACH,IAAI,YAAK,CAAC,2BAA2B,CAAC,KAAK,CAAC,UAAU,CAAC;QACrD,YAAK,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,CAAC;QACrC,CAAC,YAAK,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,OAAO,KAAK,IAAI,CAAC;QACzE,KAAK,CAAC,UAAU,KAAK,IAAI,EAAE;QAC3B,MAAM,IAAI,oCAAqB,EAAE,CAAA;KAClC;IAED;;;;;;OAMG;IACH,IAAI,aAAa,GAAgB,IAAI,CAAA;IACrC,IAAI,YAAK,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;QACtC,aAAa,GAAG,KAAK,CAAC,UAAU,CAAA;KACjC;SAAM;QACL,IAAI,KAAK,GAAG,CAAC,CAAA;;YACb,KAAoB,IAAA,KAAA,SAAA,KAAK,CAAC,UAAU,CAAC,SAAS,CAAA,gBAAA,4BAAE;gBAA3C,IAAM,KAAK,WAAA;gBACd,IAAI,KAAK,KAAK,KAAK,CAAC,YAAY,EAAE;oBAChC,aAAa,GAAG,KAAK,CAAA;oBACrB,MAAK;iBACN;gBACD,KAAK,EAAE,CAAA;aACR;;;;;;;;;KACF;IAED;;;OAGG;IACH,IAAI,MAAY,CAAA;IAChB,IAAI,aAAa,KAAK,IAAI,EAAE;QAC1B,MAAM,GAAG,KAAK,CAAC,UAAU,CAAA;KAC1B;SAAM;QACL,IAAI,aAAa,CAAC,OAAO,KAAK,IAAI,EAAE;YAClC,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;SACxC;QACD,MAAM,GAAG,aAAa,CAAC,OAAO,CAAA;KAC/B;IAED;;OAEG;IACH,uDAAmC,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,CAAA;IAEhE;;;OAGG;IACH,IAAI,YAAK,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;QACtC,aAAa,GAAG,0BAAU,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,YAAY,CAAC,CAAA;KACjE;IAED;;OAEG;IACH,IAAI,IAAI,KAAK,aAAa,EAAE;QAC1B,aAAa,GAAG,IAAI,CAAC,YAAY,CAAA;KAClC;IAED;;OAEG;IACH,IAAI,IAAI,CAAC,OAAO,KAAK,IAAI,EAAE;QACzB,mCAAe,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;KACpC;IAED;;;OAGG;IACH,IAAI,SAAS,GAAG,CAAC,aAAa,KAAK,IAAI,CAAC,CAAC;QACvC,+BAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,0BAAU,CAAC,aAAa,CAAC,CAAC,CAAA;IAEtD;;;OAGG;IACH,IAAI,YAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE;QACtC,SAAS,IAAI,+BAAe,CAAC,IAAI,CAAC,CAAA;KACnC;SAAM;QACL,SAAS,EAAE,CAAA;KACZ;IAED;;OAEG;IACH,sCAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,aAAa,CAAC,CAAA;IAE/C;;OAEG;IACH,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE;QAC1B,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;KACjC;AACH,CAAC;AAvGD,oCAuGC;AAED;;;;GAIG;AACH,SAAgB,uBAAuB,CAAC,KAAY;;IAClD;QACE,GAAC,MAAM,CAAC,QAAQ,IAAG;YAEjB,IAAM,SAAS,GAAG,KAAK,CAAC,uBAAuB,CAAA;YAC/C,IAAI,WAAW,GAAG,2CAA2B,CAAC,SAAS,CAAC,CAAA;YAExD,OAAO;gBACL,IAAI,EAAE;oBACJ,OAAO,WAAW,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,KAAK,CAAC,EAAE;wBAC5D,WAAW,GAAG,0CAA0B,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;qBACjE;oBAED,IAAI,WAAW,KAAK,IAAI,EAAE;wBACxB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;qBACnC;yBAAM;wBACL,IAAM,MAAM,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,CAAA;wBAClD,WAAW,GAAG,0CAA0B,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;wBAChE,OAAO,MAAM,CAAA;qBACd;gBACH,CAAC;aACF,CAAA;QACH,CAAC;WACF;AACH,CAAC;AAxBD,0DAwBC;AAED;;;;GAIG;AACH,SAAgB,gCAAgC,CAAC,KAAY;;IAC3D;QACE,GAAC,MAAM,CAAC,QAAQ,IAAG;YAEjB,IAAM,SAAS,GAAG,KAAK,CAAC,uBAAuB,CAAA;YAC/C,IAAI,WAAW,GAAG,2CAA2B,CAAC,SAAS,CAAC,CAAA;YAExD,OAAO;gBACL,IAAI,EAAE;oBACJ,OAAO,WAAW,IAAI,CAAC,0BAA0B,CAAC,WAAW,EAAE,KAAK,CAAC,EAAE;wBACrE,WAAW,GAAG,0CAA0B,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;qBACjE;oBAED,IAAI,WAAW,KAAK,IAAI,EAAE;wBACxB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;qBACnC;yBAAM;wBACL,IAAM,MAAM,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,CAAA;wBAClD,WAAW,GAAG,0CAA0B,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;wBAChE,OAAO,MAAM,CAAA;qBACd;gBACH,CAAC;aACF,CAAA;QACH,CAAC;WACF;AACH,CAAC;AAxBD,4EAwBC"}