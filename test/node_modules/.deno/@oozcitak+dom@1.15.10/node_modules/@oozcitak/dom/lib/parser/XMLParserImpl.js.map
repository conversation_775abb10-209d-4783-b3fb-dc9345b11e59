{"version": 3, "file": "XMLParserImpl.js", "sourceRoot": "", "sources": ["../../src/parser/XMLParserImpl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mDAAiD;AACjD,2CAA0D;AAM1D,yCAA6D;AAC7D,0CAGqB;AACrB,2DAAyD;AAEzD;;;;GAIG;AACH;IAAA;IA4LA,CAAC;IA1LC;;;;OAIG;IACH,6BAAK,GAAL,UAAM,MAAc;;QAClB,IAAM,KAAK,GAAG,IAAI,+BAAc,CAAC,MAAM,EAAE,EAAE,sBAAsB,EAAE,IAAI,EAAE,CAAC,CAAA;QAE1E,IAAM,GAAG,GAAG,2BAAe,EAAE,CAAA;QAE7B,IAAI,OAAO,GAAS,GAAG,CAAA;QACvB,IAAI,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE,CAAA;QAC7B,OAAO,KAAK,CAAC,IAAI,KAAK,sBAAS,CAAC,GAAG,EAAE;YACnC,QAAQ,KAAK,CAAC,IAAI,EAAE;gBAClB,KAAK,sBAAS,CAAC,WAAW;oBACxB,IAAM,WAAW,GAAqB,KAAK,CAAA;oBAC3C,IAAI,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;wBACjC,MAAM,IAAI,KAAK,CAAC,uBAAuB,GAAG,WAAW,CAAC,OAAO,CAAC,CAAA;qBAC/D;oBACD,MAAK;gBACP,KAAK,sBAAS,CAAC,OAAO;oBACpB,IAAM,OAAO,GAAiB,KAAK,CAAA;oBACnC,IAAI,CAAC,2BAAe,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;wBACnC,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAA;qBACjF;oBACD,IAAI,CAAC,2BAAe,CAAC,OAAO,CAAC,KAAK,CAAC;wBACjC,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;wBAC1E,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAA;qBAC1E;oBACD,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,kBAAkB,CACvD,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;oBAC9C,MAAK;gBACP,KAAK,sBAAS,CAAC,KAAK;oBAClB,IAAM,KAAK,GAAe,KAAK,CAAA;oBAC/B,IAAI,CAAC,2BAAe,CAAC,KAAK,CAAC,IAAI,CAAC;wBAC9B,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;wBAClC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA;qBACtD;oBACD,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;oBACvD,MAAK;gBACP,KAAK,sBAAS,CAAC,OAAO;oBACpB,IAAM,OAAO,GAAiB,KAAK,CAAA;oBACnC,IAAI,CAAC,2BAAe,CAAC,OAAO,CAAC,IAAI,CAAC;wBAChC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;wBACjE,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAA;qBAC7D;oBACD,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;oBACpD,MAAK;gBACP,KAAK,sBAAS,CAAC,EAAE;oBACf,IAAM,EAAE,GAAY,KAAK,CAAA;oBACzB,IAAI,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE;wBAC/D,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAA;qBAC9E;oBACD,IAAI,CAAC,2BAAe,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;wBAC7D,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAA;qBAC5E;oBACD,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,2BAA2B,CACjD,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAA;oBACtB,MAAK;gBACP,KAAK,sBAAS,CAAC,IAAI;oBACjB,IAAM,IAAI,GAAc,KAAK,CAAA;oBAC7B,IAAI,CAAC,2BAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;wBAC/B,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAA;qBAC1D;oBACD,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;oBACpE,MAAK;gBACP,KAAK,sBAAS,CAAC,OAAO;oBACpB,IAAM,OAAO,GAAiB,KAAK,CAAA;oBAEnC,gCAAgC;oBAC1B,IAAA,gEAA0D,EAAzD,cAAM,EAAE,iBAAiD,CAAA;oBAChE,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,sBAAU,CAAC,SAAS,CAAC,EAAE;wBAC3D,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAA;qBAChE;oBACD,IAAI,MAAM,KAAK,OAAO,EAAE;wBACtB,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAA;qBAC9D;oBACD,IAAI,SAAS,GAAG,OAAO,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;oBAElD,yDAAyD;oBACzD,YAAY;oBACZ,+CAA+C;oBAC/C,IAAM,cAAc,GAA8B,EAAE,CAAA;;wBACpD,KAAkC,IAAA,oBAAA,SAAA,OAAO,CAAC,UAAU,CAAA,CAAA,gBAAA,4BAAE;4BAA3C,IAAA,wBAAmB,EAAlB,eAAO,EAAE,gBAAQ;4BAC3B,IAAI,OAAO,KAAK,OAAO,EAAE;gCACvB,SAAS,GAAG,QAAQ,CAAA;6BACrB;iCAAM;gCACC,IAAA,2DAA2D,EAA1D,iBAAS,EAAE,oBAA+C,CAAA;gCACjE,IAAI,SAAS,KAAK,OAAO,EAAE;oCACzB,IAAI,YAAY,KAAK,MAAM,EAAE;wCAC3B,SAAS,GAAG,QAAQ,CAAA;qCACrB;oCACD,cAAc,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAA;iCACxC;6BACF;yBACF;;;;;;;;;oBAED,8BAA8B;oBAC9B,IAAM,WAAW,GAAG,CAAC,SAAS,KAAK,IAAI,CAAC,CAAC;wBACvC,GAAG,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;wBAC9C,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAA;oBAElC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;oBAEhC,oBAAoB;oBACpB,IAAM,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAA;;wBAEvC,KAAkC,IAAA,oBAAA,SAAA,OAAO,CAAC,UAAU,CAAA,CAAA,gBAAA,4BAAE;4BAA3C,IAAA,wBAAmB,EAAlB,eAAO,EAAE,gBAAQ;4BACrB,IAAA,2DAA2D,EAA1D,iBAAS,EAAE,oBAA+C,CAAA;4BACjE,IAAI,YAAY,GAAkB,IAAI,CAAA;4BACtC,IAAI,SAAS,KAAK,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,IAAI,YAAY,KAAK,OAAO,CAAC,EAAE;gCAC7E,kCAAkC;gCAClC,YAAY,GAAG,iBAAc,CAAC,KAAK,CAAA;6BACpC;iCAAM;gCACL,YAAY,GAAG,WAAW,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAA;gCACxD,IAAI,YAAY,KAAK,IAAI,IAAI,WAAW,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAE;oCACzE,YAAY,GAAG,IAAI,CAAA;iCACpB;qCAAM,IAAI,YAAY,KAAK,IAAI,IAAI,SAAS,KAAK,IAAI,EAAE;oCACtD,YAAY,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,IAAI,CAAA;iCACjD;6BACF;4BACD,IAAI,YAAY,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,CAAC,EAAE;gCAChD,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAA;6BAC1D;4BACD,YAAY,CAAC,GAAG,CAAC,YAAY,EAAE,YAAY,CAAC,CAAA;4BAC5C,IAAI,YAAY,KAAK,iBAAc,CAAC,KAAK,EAAE;gCACzC,IAAI,QAAQ,KAAK,iBAAc,CAAC,KAAK,EAAE;oCACrC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAA;iCAChD;6BACF;4BACD,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,sBAAU,CAAC,YAAY,CAAC,EAAE;gCACjE,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAA;6BACrE;4BAED,IAAI,SAAS,KAAK,OAAO,IAAI,QAAQ,KAAK,EAAE,EAAE;gCAC5C,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAA;6BACvD;4BAED,IAAI,YAAY,KAAK,IAAI;gCACvB,WAAW,CAAC,cAAc,CAAC,YAAY,EAAE,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAA;;gCAEvF,WAAW,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,CAAA;yBAC1E;;;;;;;;;oBAED,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;wBACxB,OAAO,GAAS,WAAW,CAAA;qBAC5B;oBACD,MAAK;gBACP,KAAK,sBAAS,CAAC,UAAU;oBACvB,IAAM,UAAU,GAAoB,KAAK,CAAA;oBACzC,IAAI,UAAU,CAAC,IAAI,KAAK,OAAO,CAAC,QAAQ,EAAE;wBACxC,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAA;qBACrE;oBACD,0BAA0B;oBAC1B,IAAI,OAAO,CAAC,OAAO,EAAE;wBACnB,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;qBAC1B;oBACD,MAAK;aACR;YAED,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE,CAAA;SAC1B;QACD,OAAO,GAAG,CAAA;IACZ,CAAC;IAED;;;;OAIG;IACK,mCAAW,GAAnB,UAAoB,IAAY;QAC9B,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;aACpD,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;aACrB,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;IAC3B,CAAC;IAED;;;;OAIG;IACK,6CAAqB,GAA7B,UAA8B,IAAY;QACxC,OAAO,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;aACpD,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;aACrB,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAA;IAC3B,CAAC;IACH,oBAAC;AAAD,CAAC,AA5LD,IA4LC;AA5LY,sCAAa"}