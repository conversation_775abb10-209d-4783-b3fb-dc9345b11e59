{"version": 3, "file": "AbortAlgorithm.js", "sourceRoot": "", "sources": ["../../src/algorithm/AbortAlgorithm.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AACA,mDAAoD;AAEpD;;;;;GAKG;AACH,SAAgB,SAAS,CAAC,SAAoC,EAAE,MAAmB;IACjF;;;OAGG;IACH,IAAI,MAAM,CAAC,YAAY;QAAE,OAAM;IAC/B,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;AACxC,CAAC;AAPD,8BAOC;AAED;;;;;GAKG;AACH,SAAgB,YAAY,CAAC,SAAoC,EAAE,MAAmB;IACpF;;;OAGG;IACH,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;AAC3C,CAAC;AAND,oCAMC;AAED;;;;GAIG;AACH,SAAgB,iBAAiB,CAAC,MAAmB;;IACnD;;;;;;OAMG;IACH,IAAI,MAAM,CAAC,YAAY;QAAE,OAAM;IAC/B,MAAM,CAAC,YAAY,GAAG,IAAI,CAAA;;QAC1B,KAAwB,IAAA,KAAA,SAAA,MAAM,CAAC,gBAAgB,CAAA,gBAAA,4BAAE;YAA5C,IAAM,SAAS,WAAA;YAClB,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;SACvB;;;;;;;;;IACD,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAA;IAC/B,kCAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;AACpC,CAAC;AAfD,8CAeC"}