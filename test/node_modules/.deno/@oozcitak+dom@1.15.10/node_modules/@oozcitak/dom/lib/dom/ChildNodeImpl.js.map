{"version": 3, "file": "ChildNodeImpl.js", "sourceRoot": "", "sources": ["../../src/dom/ChildNodeImpl.ts"], "names": [], "mappings": ";;AACA,gCAA8B;AAC9B,0CAGqB;AAErB;;;;GAIG;AACH;IAAA;IA2JA,CAAC;IAzJC,kBAAkB;IAClB,8BAAM,GAAN;QAAO,eAA2B;aAA3B,UAA2B,EAA3B,qBAA2B,EAA3B,IAA2B;YAA3B,0BAA2B;;QAChC;;;WAGG;QACH,IAAM,OAAO,GAAG,WAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACjC,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAA;QAC9B,IAAI,MAAM,KAAK,IAAI;YAAE,OAAM;QAE3B;;;WAGG;QACH,IAAI,qBAAqB,GAAG,OAAO,CAAC,gBAAgB,CAAA;QACpD,IAAI,IAAI,GAAG,IAAI,CAAA;QACf,OAAO,IAAI,IAAI,qBAAqB,EAAE;YACpC,IAAI,GAAG,KAAK,CAAA;YACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrC,IAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACvB,IAAI,KAAK,KAAK,qBAAqB,EAAE;oBACnC,qBAAqB,GAAG,qBAAqB,CAAC,gBAAgB,CAAA;oBAC9D,IAAI,GAAG,IAAI,CAAA;oBACX,MAAK;iBACN;aACF;SACF;QAED;;;WAGG;QACH,IAAM,IAAI,GAAG,4CAAgC,CAAC,KAAK,EACjD,OAAO,CAAC,aAAa,CAAC,CAAA;QAExB;;;WAGG;QACH,IAAI,qBAAqB,KAAK,IAAI;YAChC,qBAAqB,GAAG,MAAM,CAAC,WAAW,CAAA;;YAE1C,qBAAqB,GAAG,qBAAqB,CAAC,YAAY,CAAA;QAE5D;;WAEG;QACH,8BAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,qBAAqB,CAAC,CAAA;IACzD,CAAC;IAED,kBAAkB;IAClB,6BAAK,GAAL;QAAM,eAA2B;aAA3B,UAA2B,EAA3B,qBAA2B,EAA3B,IAA2B;YAA3B,0BAA2B;;QAE/B;;;WAGG;QACH,IAAM,OAAO,GAAG,WAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACjC,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAA;QAC9B,IAAI,CAAC,MAAM;YAAE,OAAM;QAEnB;;;WAGG;QACH,IAAI,iBAAiB,GAAG,OAAO,CAAC,YAAY,CAAA;QAC5C,IAAI,IAAI,GAAG,IAAI,CAAA;QACf,OAAO,IAAI,IAAI,iBAAiB,EAAE;YAChC,IAAI,GAAG,KAAK,CAAA;YACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrC,IAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACvB,IAAI,KAAK,KAAK,iBAAiB,EAAE;oBAC/B,iBAAiB,GAAG,iBAAiB,CAAC,YAAY,CAAA;oBAClD,IAAI,GAAG,IAAI,CAAA;oBACX,MAAK;iBACN;aACF;SACF;QAED;;;WAGG;QACH,IAAM,IAAI,GAAG,4CAAgC,CAAC,KAAK,EACjD,OAAO,CAAC,aAAa,CAAC,CAAA;QAExB;;WAEG;QACH,8BAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAA;IACrD,CAAC;IAED,kBAAkB;IAClB,mCAAW,GAAX;QAAY,eAA2B;aAA3B,UAA2B,EAA3B,qBAA2B,EAA3B,IAA2B;YAA3B,0BAA2B;;QAErC;;;WAGG;QACH,IAAM,OAAO,GAAG,WAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACjC,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAA;QAC9B,IAAI,CAAC,MAAM;YAAE,OAAM;QAEnB;;;WAGG;QACH,IAAI,iBAAiB,GAAG,OAAO,CAAC,YAAY,CAAA;QAC5C,IAAI,IAAI,GAAG,IAAI,CAAA;QACf,OAAO,IAAI,IAAI,iBAAiB,EAAE;YAChC,IAAI,GAAG,KAAK,CAAA;YACZ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrC,IAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACvB,IAAI,KAAK,KAAK,iBAAiB,EAAE;oBAC/B,iBAAiB,GAAG,iBAAiB,CAAC,YAAY,CAAA;oBAClD,IAAI,GAAG,IAAI,CAAA;oBACX,MAAK;iBACN;aACF;SACF;QAED;;;WAGG;QACH,IAAM,IAAI,GAAG,4CAAgC,CAAC,KAAK,EACjD,OAAO,CAAC,aAAa,CAAC,CAAA;QAExB;;;;;WAKG;QACH,IAAI,OAAO,CAAC,OAAO,KAAK,MAAM;YAC5B,4BAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;;YAEvC,8BAAkB,CAAC,IAAI,EAAE,MAAM,EAAE,iBAAiB,CAAC,CAAA;IACvD,CAAC;IAED,kBAAkB;IAClB,8BAAM,GAAN;QACE;;;WAGG;QACH,IAAM,OAAO,GAAG,WAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACjC,IAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAA;QAC9B,IAAI,CAAC,MAAM;YAAE,OAAM;QAEnB,2BAAe,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;IAClC,CAAC;IAEH,oBAAC;AAAD,CAAC,AA3JD,IA2JC;AA3JY,sCAAa"}