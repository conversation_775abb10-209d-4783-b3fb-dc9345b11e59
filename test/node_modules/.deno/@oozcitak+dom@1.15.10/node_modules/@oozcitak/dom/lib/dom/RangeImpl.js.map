{"version": 3, "file": "RangeImpl.js", "sourceRoot": "", "sources": ["../../src/dom/RangeImpl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qCAA+B;AAC/B,2CAGqB;AACrB,yDAAuD;AACvD,+CAGuB;AACvB,0CAOqB;AACrB,gEAA8D;AAC9D,gCAA+B;AAE/B;;GAEG;AACH;IAA+B,6BAAiB;IAe9C;;OAEG;IACH;QAAA,YACE,iBAAO,SAWR;QATC;;;WAGG;QACH,IAAM,GAAG,GAAG,aAAG,CAAC,MAAM,CAAC,mBAAmB,CAAA;QAC1C,KAAI,CAAC,MAAM,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;QACtB,KAAI,CAAC,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;QAEpB,aAAG,CAAC,SAAS,CAAC,GAAG,CAAC,KAAI,CAAC,CAAA;;IACzB,CAAC;IAGD,sBAAI,8CAAuB;QAD3B,kBAAkB;aAClB;YACE;;;;;eAKG;YACH,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YAC9B,OAAO,CAAC,6BAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,EAAE;gBACxD,IAAI,SAAS,CAAC,OAAO,KAAK,IAAI,EAAE;oBAC9B,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAA;iBACzC;gBACD,SAAS,GAAG,SAAS,CAAC,OAAO,CAAA;aAC9B;YAED,OAAO,SAAS,CAAA;QAClB,CAAC;;;OAAA;IAED,kBAAkB;IAClB,4BAAQ,GAAR,UAAS,IAAU,EAAE,MAAc;QACjC;;;WAGG;QACH,6BAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;IACvC,CAAC;IAED,kBAAkB;IAClB,0BAAM,GAAN,UAAO,IAAU,EAAE,MAAc;QAC/B;;;WAGG;QACH,2BAAe,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAA;IACrC,CAAC;IAED,kBAAkB;IAClB,kCAAc,GAAd,UAAe,IAAU;QACvB;;;;;WAKG;QACH,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;QACzB,IAAI,MAAM,KAAK,IAAI;YACjB,MAAM,IAAI,mCAAoB,EAAE,CAAA;QAElC,6BAAiB,CAAC,IAAI,EAAE,MAAM,EAC5B,sBAAU,CAAC,IAAI,CAAC,CAAC,CAAA;IACrB,CAAC;IAED,kBAAkB;IAClB,iCAAa,GAAb,UAAc,IAAU;QACtB;;;;;WAKG;QACH,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;QACzB,IAAI,MAAM,KAAK,IAAI;YACjB,MAAM,IAAI,mCAAoB,EAAE,CAAA;QAElC,6BAAiB,CAAC,IAAI,EAAE,MAAM,EAC5B,sBAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;IACzB,CAAC;IAED,kBAAkB;IAClB,gCAAY,GAAZ,UAAa,IAAU;QACrB;;;;;WAKG;QACH,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;QACzB,IAAI,MAAM,KAAK,IAAI;YACjB,MAAM,IAAI,mCAAoB,EAAE,CAAA;QAElC,2BAAe,CAAC,IAAI,EAAE,MAAM,EAC1B,sBAAU,CAAC,IAAI,CAAC,CAAC,CAAA;IACrB,CAAC;IAED,kBAAkB;IAClB,+BAAW,GAAX,UAAY,IAAU;QACpB;;;;;WAKG;QACH,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;QACzB,IAAI,MAAM,KAAK,IAAI;YACjB,MAAM,IAAI,mCAAoB,EAAE,CAAA;QAElC,2BAAe,CAAC,IAAI,EAAE,MAAM,EAC1B,sBAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;IACzB,CAAC;IAED,kBAAkB;IAClB,4BAAQ,GAAR,UAAS,OAA6B;QACpC;;;WAGG;QACH,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAA;SACxB;aAAM;YACL,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;SACxB;IACH,CAAC;IAED,kBAAkB;IAClB,8BAAU,GAAV,UAAW,IAAU;QACnB;;;WAGG;QACH,wBAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC1B,CAAC;IAED,kBAAkB;IAClB,sCAAkB,GAAlB,UAAmB,IAAU;QAC3B;;;;;WAKG;QACH,IAAI,YAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAChC,MAAM,IAAI,mCAAoB,EAAE,CAAA;QAElC,IAAM,MAAM,GAAG,2BAAe,CAAC,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;QACvB,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;IAC5B,CAAC;IAED,kBAAkB;IAClB,yCAAqB,GAArB,UAAsB,GAAiB,EAAE,WAAkB;QACzD;;;;;;;WAOG;QACH,IAAI,GAAG,KAAK,yBAAY,CAAC,YAAY,IAAI,GAAG,KAAK,yBAAY,CAAC,UAAU;YACtE,GAAG,KAAK,yBAAY,CAAC,QAAQ,IAAI,GAAG,KAAK,yBAAY,CAAC,UAAU;YAChE,MAAM,IAAI,gCAAiB,EAAE,CAAA;QAE/B;;;WAGG;QACH,IAAI,sBAAU,CAAC,IAAI,CAAC,KAAK,sBAAU,CAAC,WAAW,CAAC;YAC9C,MAAM,IAAI,iCAAkB,EAAE,CAAA;QAEhC;;;;;;;;;;;;;;WAcG;QACH,IAAI,SAAwB,CAAA;QAC5B,IAAI,UAAyB,CAAA;QAE7B,QAAQ,GAAG,EAAE;YACX,KAAK,yBAAY,CAAC,YAAY;gBAC5B,SAAS,GAAG,IAAI,CAAC,MAAM,CAAA;gBACvB,UAAU,GAAG,WAAW,CAAC,MAAM,CAAA;gBAC/B,MAAK;YACP,KAAK,yBAAY,CAAC,UAAU;gBAC1B,SAAS,GAAG,IAAI,CAAC,IAAI,CAAA;gBACrB,UAAU,GAAG,WAAW,CAAC,MAAM,CAAA;gBAC/B,MAAK;YACP,KAAK,yBAAY,CAAC,QAAQ;gBACxB,SAAS,GAAG,IAAI,CAAC,IAAI,CAAA;gBACrB,UAAU,GAAG,WAAW,CAAC,IAAI,CAAA;gBAC7B,MAAK;YACP,KAAK,yBAAY,CAAC,UAAU;gBAC1B,SAAS,GAAG,IAAI,CAAC,MAAM,CAAA;gBACvB,UAAU,GAAG,WAAW,CAAC,IAAI,CAAA;gBAC7B,MAAK;YACP,0BAA0B;YAC1B;gBACE,MAAM,IAAI,gCAAiB,EAAE,CAAA;SAChC;QAED;;;;;;;;WAQG;QACH,IAAM,QAAQ,GAAG,kCAAsB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAA;QAE9D,IAAI,QAAQ,KAAK,6BAAgB,CAAC,MAAM,EAAE;YACxC,OAAO,CAAC,CAAC,CAAA;SACV;aAAM,IAAI,QAAQ,KAAK,6BAAgB,CAAC,KAAK,EAAE;YAC9C,OAAO,CAAC,CAAA;SACT;aAAM;YACL,OAAO,CAAC,CAAA;SACT;IACH,CAAC;IAED,kBAAkB;IAClB,kCAAc,GAAd;;QACE;;;;;WAKG;QACH,IAAI,2BAAe,CAAC,IAAI,CAAC;YAAE,OAAM;QAEjC,IAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAA;QACzC,IAAM,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAA;QAC7C,IAAM,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAA;QACrC,IAAM,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAA;QAEzC;;;;;;WAMG;QACH,IAAI,iBAAiB,KAAK,eAAe;YACvC,YAAK,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,EAAE;YAC9C,qCAAyB,CAAC,iBAAiB,EACzC,mBAAmB,EAAE,iBAAiB,GAAG,mBAAmB,EAAE,EAAE,CAAC,CAAA;YACnE,OAAM;SACP;QAED;;;;WAIG;QACH,IAAM,aAAa,GAAW,EAAE,CAAA;;YAChC,KAAmB,IAAA,KAAA,SAAA,mCAAuB,CAAC,IAAI,CAAC,CAAA,gBAAA,4BAAE;gBAA7C,IAAM,IAAI,WAAA;gBACb,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;gBAC3B,IAAI,MAAM,KAAK,IAAI,IAAI,6BAAiB,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;oBACtD,SAAQ;iBACT;gBACD,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;aACzB;;;;;;;;;QAED,IAAI,OAAa,CAAA;QACjB,IAAI,SAAiB,CAAA;QAErB,IAAI,6BAAiB,CAAC,eAAe,EAAE,iBAAiB,EAAE,IAAI,CAAC,EAAE;YAC/D;;;;eAIG;YACH,OAAO,GAAG,iBAAiB,CAAA;YAC3B,SAAS,GAAG,mBAAmB,CAAA;SAChC;aAAM;YACL;;;;;;;eAOG;YACH,IAAI,aAAa,GAAG,iBAAiB,CAAA;YACrC,OAAO,aAAa,CAAC,OAAO,KAAK,IAAI;gBACnC,CAAC,6BAAiB,CAAC,eAAe,EAAE,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;gBAClE,aAAa,GAAG,aAAa,CAAC,OAAO,CAAA;aACtC;YACD,0BAA0B;YAC1B,IAAI,aAAa,CAAC,OAAO,KAAK,IAAI,EAAE;gBAClC,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;aACxC;YACD,OAAO,GAAG,aAAa,CAAC,OAAO,CAAA;YAC/B,SAAS,GAAG,sBAAU,CAAC,aAAa,CAAC,GAAG,CAAC,CAAA;SAC1C;QAED;;;;;WAKG;QACH,IAAI,YAAK,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,EAAE;YAChD,qCAAyB,CAAC,iBAAiB,EACzC,mBAAmB,EACnB,2BAAe,CAAC,iBAAiB,CAAC,GAAG,mBAAmB,EAAE,EAAE,CAAC,CAAA;SAChE;;YAED;;;eAGG;YACH,KAAmB,IAAA,kBAAA,SAAA,aAAa,CAAA,4CAAA,uEAAE;gBAA7B,IAAM,IAAI,0BAAA;gBACb,0BAA0B;gBAC1B,IAAI,IAAI,CAAC,OAAO,EAAE;oBAChB,2BAAe,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;iBACpC;aACF;;;;;;;;;QAED;;;;WAIG;QACH,IAAI,YAAK,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE;YAC9C,qCAAyB,CAAC,eAAe,EACvC,CAAC,EAAE,iBAAiB,EAAE,EAAE,CAAC,CAAA;SAC5B;QAED;;WAEG;QACH,IAAI,CAAC,MAAM,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;QAClC,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;IAClC,CAAC;IAED,kBAAkB;IAClB,mCAAe,GAAf;QACE;;;WAGG;QACH,OAAO,yBAAa,CAAC,IAAI,CAAC,CAAA;IAC5B,CAAC;IAED,kBAAkB;IAClB,iCAAa,GAAb;QACE;;;WAGG;QACH,OAAO,kCAAsB,CAAC,IAAI,CAAC,CAAA;IACrC,CAAC;IAED,kBAAkB;IAClB,8BAAU,GAAV,UAAW,IAAU;QACnB;;;WAGG;QACH,OAAO,wBAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACjC,CAAC;IAED,kBAAkB;IAClB,oCAAgB,GAAhB,UAAiB,SAAe;;;YAC9B;;;eAGG;YACH,KAAmB,IAAA,KAAA,SAAA,4CAAgC,CAAC,IAAI,CAAC,CAAA,gBAAA,4BAAE;gBAAtD,IAAM,IAAI,WAAA;gBACb,IAAI,CAAC,YAAK,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;oBAC3B,MAAM,IAAI,gCAAiB,EAAE,CAAA;iBAC9B;aACF;;;;;;;;;QAED;;;WAGG;QACH,IAAI,YAAK,CAAC,cAAc,CAAC,SAAS,CAAC;YACjC,YAAK,CAAC,kBAAkB,CAAC,SAAS,CAAC;YACnC,YAAK,CAAC,sBAAsB,CAAC,SAAS,CAAC,EAAE;YACzC,MAAM,IAAI,mCAAoB,EAAE,CAAA;SACjC;QAED;;WAEG;QACH,IAAM,QAAQ,GAAG,yBAAa,CAAC,IAAI,CAAC,CAAA;QAEpC;;WAEG;QACH,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,EAAE;YACpC,+BAAmB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;SACrC;QAED;;;WAGG;QACH,wBAAY,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;QAC7B,2BAAe,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAA;QAEpC;;WAEG;QACH,wBAAY,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;IAC/B,CAAC;IAED,kBAAkB;IAClB,8BAAU,GAAV;QACE;;;WAGG;QACH,OAAO,wBAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;IAC7C,CAAC;IAED,kBAAkB;IAClB,0BAAM,GAAN;QACE;;;;WAIG;QACH,aAAG,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;IAC5B,CAAC;IAED,kBAAkB;IAClB,kCAAc,GAAd,UAAe,IAAU,EAAE,MAAc;QACvC;;WAEG;QACH,IAAI,yBAAa,CAAC,IAAI,CAAC,KAAK,sBAAU,CAAC,IAAI,CAAC,EAAE;YAC5C,OAAO,KAAK,CAAA;SACb;QAED;;;;WAIG;QACH,IAAI,YAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAChC,MAAM,IAAI,mCAAoB,EAAE,CAAA;QAClC,IAAI,MAAM,GAAG,2BAAe,CAAC,IAAI,CAAC;YAChC,MAAM,IAAI,6BAAc,EAAE,CAAA;QAE5B;;WAEG;QACH,IAAM,EAAE,GAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QACxC,IAAI,kCAAsB,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,6BAAgB,CAAC,MAAM;YACrE,kCAAsB,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,6BAAgB,CAAC,KAAK,EAAE;YAClE,OAAO,KAAK,CAAA;SACb;QAED;;WAEG;QACH,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,gCAAY,GAAZ,UAAa,IAAU,EAAE,MAAc;QACrC;;;;;;WAMG;QACH,IAAI,yBAAa,CAAC,IAAI,CAAC,KAAK,sBAAU,CAAC,IAAI,CAAC;YAC1C,MAAM,IAAI,iCAAkB,EAAE,CAAA;QAChC,IAAI,YAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAChC,MAAM,IAAI,mCAAoB,EAAE,CAAA;QAClC,IAAI,MAAM,GAAG,2BAAe,CAAC,IAAI,CAAC;YAChC,MAAM,IAAI,6BAAc,EAAE,CAAA;QAE5B;;;;WAIG;QACH,IAAM,EAAE,GAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QACxC,IAAI,kCAAsB,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,6BAAgB,CAAC,MAAM,EAAE;YACvE,OAAO,CAAC,CAAC,CAAA;SACV;aAAM,IAAI,kCAAsB,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,6BAAgB,CAAC,KAAK,EAAE;YAC3E,OAAO,CAAC,CAAA;SACT;aAAM;YACL,OAAO,CAAC,CAAA;SACT;IACH,CAAC;IAED,kBAAkB;IAClB,kCAAc,GAAd,UAAe,IAAU;QACvB;;WAEG;QACH,IAAI,yBAAa,CAAC,IAAI,CAAC,KAAK,sBAAU,CAAC,IAAI,CAAC,EAAE;YAC5C,OAAO,KAAK,CAAA;SACb;QAED;;;WAGG;QACH,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAA;QAC3B,IAAI,MAAM,KAAK,IAAI;YAAE,OAAO,IAAI,CAAA;QAEhC;;WAEG;QACH,IAAM,MAAM,GAAG,sBAAU,CAAC,IAAI,CAAC,CAAA;QAE/B;;;WAGG;QACH,IAAI,kCAAsB,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,6BAAgB,CAAC,MAAM;YACjF,kCAAsB,CAAC,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,6BAAgB,CAAC,KAAK,EAAE;YACtF,OAAO,IAAI,CAAA;SACZ;QAED;;WAEG;QACH,OAAO,KAAK,CAAA;IACd,CAAC;IAED,4BAAQ,GAAR;;QACE;;WAEG;QACH,IAAI,CAAC,GAAG,EAAE,CAAA;QAEV;;;;;WAKG;QACH,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,QAAQ,IAAI,YAAK,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YAC1E,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;SAC3E;QAED;;;;WAIG;QACH,IAAI,YAAK,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;YACrC,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;SACxD;;YAED;;;eAGG;YACH,KAAoB,IAAA,KAAA,SAAA,mCAAuB,CAAC,IAAI,CAAC,CAAA,gBAAA,4BAAE;gBAA9C,IAAM,KAAK,WAAA;gBACd,IAAI,YAAK,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;oBAC3B,CAAC,IAAI,KAAK,CAAC,KAAK,CAAA;iBACjB;aACF;;;;;;;;;QAED;;;;WAIG;QACH,IAAI,YAAK,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACnC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;SACvD;QAED;;WAEG;QACH,OAAO,CAAC,CAAA;IACV,CAAC;IAED;;;;;OAKG;IACI,iBAAO,GAAd,UAAe,KAAqB,EAAE,GAAmB;QACvD,IAAM,KAAK,GAAG,IAAI,SAAS,EAAE,CAAA;QAC7B,IAAI,KAAK;YAAE,KAAK,CAAC,MAAM,GAAG,KAAK,CAAA;QAC/B,IAAI,GAAG;YAAE,KAAK,CAAC,IAAI,GAAG,GAAG,CAAA;QACzB,OAAO,KAAK,CAAA;IACd,CAAC;IA7mBM,wBAAc,GAAG,CAAC,CAAA;IAClB,sBAAY,GAAG,CAAC,CAAA;IAChB,oBAAU,GAAG,CAAC,CAAA;IACd,sBAAY,GAAG,CAAC,CAAA;IA2mBzB,gBAAC;CAAA,AAhnBD,CAA+B,qCAAiB,GAgnB/C;AAhnBY,8BAAS;AAknBtB;;GAEG;AACH,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAA;AACzD,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,cAAc,EAAE,CAAC,CAAC,CAAA;AACvD,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,YAAY,EAAE,CAAC,CAAC,CAAA;AACrD,iCAAe,CAAC,SAAS,CAAC,SAAS,EAAE,cAAc,EAAE,CAAC,CAAC,CAAA"}