{"version": 3, "file": "TraverserImpl.js", "sourceRoot": "", "sources": ["../../src/dom/TraverserImpl.ts"], "names": [], "mappings": ";;AAAA,2CAAsE;AAEtE;;;GAGG;AACH;IAOE;;;;OAIG;IACH,uBAAsB,IAAU;QAC9B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;QACxB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QACjB,IAAI,CAAC,WAAW,GAAG,uBAAU,CAAC,GAAG,CAAA;QACjC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;IACrB,CAAC;IAGD,sBAAI,+BAAI;QADR,kBAAkB;aAClB,cAAmB,OAAO,IAAI,CAAC,KAAK,CAAA,CAAC,CAAC;;;OAAA;IAGtC,sBAAI,qCAAU;QADd,kBAAkB;aAClB,cAA+B,OAAO,IAAI,CAAC,WAAW,CAAA,CAAC,CAAC;;;OAAA;IAGxD,sBAAI,iCAAM;QADV,kBAAkB;aAClB,cAAkC,OAAO,IAAI,CAAC,OAAO,CAAA,CAAC,CAAC;;;OAAA;IAEzD,oBAAC;AAAD,CAAC,AA5BD,IA4BC;AA5BqB,sCAAa"}