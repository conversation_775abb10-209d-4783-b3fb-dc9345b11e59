{"version": 3, "file": "NodeIteratorAlgorithm.js", "sourceRoot": "", "sources": ["../../src/algorithm/NodeIteratorAlgorithm.ts"], "names": [], "mappings": ";;AAAA,0CAAoC;AACpC,gDAAoE;AAEpE,2DAAuD;AACvD,iDAA8E;AAE9E;;;;;;;GAOG;AACH,SAAgB,qBAAqB,CAAC,QAAsB,EAAE,OAAgB;IAC5E;;;OAGG;IACH,IAAI,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAA;IAC9B,IAAI,UAAU,GAAG,QAAQ,CAAC,uBAAuB,CAAA;IAEjD;;OAEG;IACH,OAAO,IAAI,EAAE;QACX;;WAEG;QACH,IAAI,OAAO,EAAE;YACX;;eAEG;YACH,IAAI,CAAC,UAAU,EAAE;gBACf;;;;mBAIG;gBACH,IAAM,QAAQ,GAAG,qCAAqB,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;gBAC5D,IAAI,QAAQ,EAAE;oBACZ,IAAI,GAAG,QAAQ,CAAA;iBAChB;qBAAM;oBACL,OAAO,IAAI,CAAA;iBACZ;aACF;iBAAM;gBACL;;mBAEG;gBACH,UAAU,GAAG,KAAK,CAAA;aACnB;SACF;aAAM;YACL;;eAEG;YACH,IAAI,UAAU,EAAE;gBACd;;;;mBAIG;gBACH,IAAM,QAAQ,GAAG,qCAAqB,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;gBAC3D,IAAI,QAAQ,EAAE;oBACZ,IAAI,GAAG,QAAQ,CAAA;iBAChB;qBAAM;oBACL,OAAO,IAAI,CAAA;iBACZ;aACF;iBAAM;gBACL;;mBAEG;gBACH,UAAU,GAAG,IAAI,CAAA;aAClB;SACF;QAED;;;WAGG;QACH,IAAM,MAAM,GAAG,qCAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QAC/C,IAAI,MAAM,KAAK,yBAAY,CAAC,MAAM,EAAE;YAClC,MAAK;SACN;KACF;IAED;;;;OAIG;IACH,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAA;IAC1B,QAAQ,CAAC,uBAAuB,GAAG,UAAU,CAAA;IAC7C,OAAO,IAAI,CAAA;AACb,CAAC;AA/ED,sDA+EC;AAED;;GAEG;AACH,SAAgB,yBAAyB;IACvC,OAAO,aAAG,CAAC,MAAM,CAAC,aAAa,CAAA;AACjC,CAAC;AAFD,8DAEC"}