{"version": 3, "file": "NodeListImpl.js", "sourceRoot": "", "sources": ["../../src/dom/NodeListImpl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,qCAA+B;AAE/B,uCAAyC;AACzC,0CAA+C;AAE/C;;GAEG;AACH;IAOE;;;;OAIG;IACH,sBAAoB,IAAU;QAV9B,UAAK,GAAY,IAAI,CAAA;QAErB,YAAO,GAAiC,IAAI,CAAA;QAC5C,YAAO,GAAG,CAAC,CAAA;QAQT,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QAEjB,OAAO,IAAI,KAAK,CAAe,IAAI,EAAE,IAAI,CAAC,CAAA;IAC5C,CAAC;IAGD,sBAAI,gCAAM;QADV,kBAAkB;aAClB;YACE;;;eAGG;YACH,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAA;QAClC,CAAC;;;OAAA;IAED,kBAAkB;IAClB,2BAAI,GAAJ,UAAK,KAAa;QAChB;;;;WAIG;QACH,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC;YAAE,OAAO,IAAI,CAAA;QAErD,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,GAAG,CAAC,CAAA;YACT,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAA;YACjC,OAAO,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,EAAE;gBACnC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAA;gBACxB,CAAC,EAAE,CAAA;aACJ;YACD,OAAO,IAAI,CAAA;SACZ;aACI;YACH,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAA;YACvB,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAA;YAChC,OAAO,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,EAAE;gBACnC,IAAI,GAAG,IAAI,CAAC,gBAAgB,CAAA;gBAC5B,CAAC,EAAE,CAAA;aACJ;YACD,OAAO,IAAI,CAAA;SACZ;IACH,CAAC;IAKD,kBAAkB;IAClB,2BAAI,GAAJ;;QACE;YACE,GAAC,MAAM,CAAC,QAAQ,IAAG;gBACjB,IAAI,KAAK,GAAG,CAAC,CAAA;gBAEb,OAAO;oBACL,IAAI,EAAE;wBACJ,IAAI,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE;4BACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;yBACnC;6BAAM;4BACL,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,CAAA;yBACvC;oBACH,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;iBACb,CAAA;YACH,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;eACb;IACH,CAAC;IAED,kBAAkB;IAClB,6BAAM,GAAN;;QACE;YACE,GAAC,MAAM,CAAC,QAAQ,IAAG;gBAEjB,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAA;gBAElC,OAAO;oBACL,IAAI,EAAJ;wBACE,OAAO,EAAE,CAAC,IAAI,EAAE,CAAA;oBAClB,CAAC;iBACF,CAAA;YACH,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;eACb;IACH,CAAC;IAED,kBAAkB;IAClB,8BAAO,GAAP;;QACE;YACE,GAAC,MAAM,CAAC,QAAQ,IAAG;gBAEjB,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAA;gBAClC,IAAI,KAAK,GAAG,CAAC,CAAA;gBAEb,OAAO;oBACL,IAAI,EAAJ;wBACE,IAAM,QAAQ,GAAG,EAAE,CAAC,IAAI,EAAE,CAAA;wBAC1B,IAAI,QAAQ,CAAC,IAAI,EAAE;4BACjB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;yBACnC;6BAAM;4BACL,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,KAAK,EAAE,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAA;yBACzD;oBACH,CAAC;iBACF,CAAA;YACH,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;eACb;IACH,CAAC;IAED,kBAAkB;IAClB,uBAAC,MAAM,CAAC,QAAQ,CAAC,GAAjB;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAA;IAChD,CAAC;IAED,kBAAkB;IAClB,8BAAO,GAAP,UAAQ,QAA8D,EACpE,OAAa;;QACb,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,OAAO,GAAG,aAAG,CAAC,MAAM,CAAA;SACrB;QAED,IAAI,KAAK,GAAG,CAAC,CAAA;;YACb,KAAmB,IAAA,KAAA,SAAA,IAAI,CAAC,KAAK,CAAC,SAAS,CAAA,gBAAA,4BAAE;gBAApC,IAAM,IAAI,WAAA;gBACb,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,CAAA;aAC5C;;;;;;;;;IACH,CAAC;IAED;;OAEG;IACH,0BAAG,GAAH,UAAI,MAAgB,EAAE,GAAgB,EAAE,QAAa;QACnD,IAAI,CAAC,eAAQ,CAAC,GAAG,CAAC,EAAE;YAClB,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAA;SAC1C;QAED,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;QACzB,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;YAChB,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAA;SAC1C;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,SAAS,CAAA;IACxC,CAAC;IAED;;OAEG;IACH,0BAAG,GAAH,UAAI,MAAgB,EAAE,GAAgB,EAAE,KAAW,EAAE,QAAa;QAChE,IAAI,CAAC,eAAQ,CAAC,GAAG,CAAC,EAAE;YAClB,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;SACjD;QAED,IAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAA;QACzB,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;YAChB,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;SACjD;QAED,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,SAAS,CAAA;QAC5C,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAA;QAEvB,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,4BAAgB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;YAC3C,OAAO,IAAI,CAAA;SACZ;aAAM;YACL,OAAO,KAAK,CAAA;SACb;IACH,CAAC;IAED;;;;OAIG;IACI,oBAAO,GAAd,UAAe,IAAU;QACvB,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,CAAA;IAC/B,CAAC;IACH,mBAAC;AAAD,CAAC,AAtLD,IAsLC;AAtLY,oCAAY"}