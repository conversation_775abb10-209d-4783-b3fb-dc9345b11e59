{"version": 3, "file": "DOMAlgorithm.js", "sourceRoot": "", "sources": ["../../src/algorithm/DOMAlgorithm.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,0CAAoC;AAEpC,iDAGwB;AACxB,gCAA+B;AAC/B,6DAG8B;AAE9B,IAAM,eAAe,GAAG,IAAI,GAAG,EAAE,CAAA;AAEjC;;;;;GAKG;AACH,SAAgB,oBAAoB,CAAC,WAAiB,EAAE,SAAe;IACrE,mBAAmB;AACrB,CAAC;AAFD,oDAEC;AAED;;;;;;;GAOG;AACH,SAAgB,mBAAmB,CAAC,IAAU,EAAE,IAAU,EAAE,QAAkB,EAC5E,iBAA0B;IAC1B,mBAAmB;AACrB,CAAC;AAHD,kDAGC;AAED;;;;;GAKG;AACH,SAAgB,oBAAoB,CAAC,IAAU,EAAE,WAAqB;IACpE,mBAAmB;AACrB,CAAC;AAFD,oDAEC;AAED;;;;;;;;GAQG;AACH,SAAgB,2BAA2B,CAAC,OAAgB,EAAE,SAAiB,EAC7E,QAAuB,EAAE,KAAoB,EAC7C,SAAwB;;IAExB,oBAAoB;IACpB,IAAI,aAAG,CAAC,QAAQ,CAAC,KAAK,EAAE;QACtB,oBAAoB,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,CAAA;QAClF,gBAAgB,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,CAAA;KAC/E;IACD,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,CAAC,CAAA;;QAErE,mBAAmB;QACnB,KAAmB,IAAA,KAAA,SAAA,OAAO,CAAC,qBAAqB,CAAA,gBAAA,4BAAE;YAA7C,IAAM,IAAI,WAAA;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,CAAC,CAAA;SACnE;;;;;;;;;AACH,CAAC;AAfD,kEAeC;AAED;;;;GAIG;AACH,SAAgB,qBAAqB,CAAC,YAAkB;IACtD,mBAAmB;AACrB,CAAC;AAFD,sDAEC;AAED;;;;;GAKG;AACH,SAAgB,mCAAmC,CAAC,YAA0B,EAC5E,WAAiB;IACjB,kBAAkB,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,EAAE,WAAW,CAAC,CAAA;AAClE,CAAC;AAHD,kFAGC;AAED;;;;;GAKG;AACH,SAAgB,sBAAsB,CAAC,aAAqB;IAC1D,OAAO,eAAe,CAAC,GAAG,CAAC,aAAa,CAAC,CAAA;AAC3C,CAAC;AAFD,wDAEC;AAED;;;;GAIG;AACH,SAAgB,sBAAsB,CAAC,aAAqB;IAC1D,OAAO,eAAe,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,IAAI,GAAG,EAAE,CAAA;AACxD,CAAC;AAFD,wDAEC;AAED;;;;GAIG;AACH,SAAgB,6BAA6B,CAAC,KAAY;IACxD,mBAAmB;AACrB,CAAC;AAFD,sEAEC;AAED;;;;GAIG;AACH,SAAgB,kCAAkC,CAAC,MAAY;IAC7D,mBAAmB;AACrB,CAAC;AAFD,gFAEC;AAED;;GAEG;AACH,SAAS,kBAAkB,CAAC,YAA0B,EACpD,eAAqB;IACrB;;;OAGG;IACH,IAAI,eAAe,KAAK,YAAY,CAAC,KAAK;QACxC,CAAC,iCAAiB,CAAC,YAAY,CAAC,UAAU,EAAE,eAAe,EAAE,IAAI,CAAC,EAAE;QACpE,OAAM;KACP;IAED;;OAEG;IACH,IAAI,YAAY,CAAC,uBAAuB,EAAE;QACxC;;;;WAIG;QACH,OAAO,IAAI,EAAE;YACX,IAAM,QAAQ,GAAG,qCAAqB,CAAC,YAAY,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;YAC3E,IAAI,QAAQ,KAAK,IAAI;gBACnB,mCAAmB,CAAC,YAAY,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC;gBACvD,CAAC,mCAAmB,CAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE;gBACvD;;;mBAGG;gBACH,YAAY,CAAC,UAAU,GAAG,QAAQ,CAAA;gBAClC,OAAM;aACP;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;gBAC5B;;mBAEG;gBACH,YAAY,CAAC,uBAAuB,GAAG,KAAK,CAAA;gBAC5C,OAAM;aACP;SACF;KACF;IAED;;;;;OAKG;IACH,IAAI,eAAe,CAAC,gBAAgB,KAAK,IAAI,EAAE;QAC7C,IAAI,eAAe,CAAC,OAAO,KAAK,IAAI,EAAE;YACpC,YAAY,CAAC,UAAU,GAAG,eAAe,CAAC,OAAO,CAAA;SAClD;KACF;SAAM;QACL,IAAI,aAAa,GAAG,eAAe,CAAC,gBAAgB,CAAA;QACpD,IAAI,SAAS,GAAG,2CAA2B,CAAC,eAAe,CAAC,gBAAgB,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;QAC1F,OAAO,SAAS,KAAK,IAAI,EAAE;YACzB,IAAI,SAAS,KAAK,IAAI,EAAE;gBACtB,aAAa,GAAG,SAAS,CAAA;aAC1B;YACD,+CAA+C;YAC/C,SAAS,GAAG,0CAA0B,CAAC,eAAe,CAAC,gBAAgB,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;SACjG;QACD,YAAY,CAAC,UAAU,GAAG,aAAa,CAAA;KACxC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,OAAgB,EAAE,SAAiB,EAC3D,QAAuB,EAAE,KAAoB,EAAE,SAAwB;IACvE;;;;;;;;;OASG;IACH,IAAI,YAAK,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,IAAI,EAAE;QACvE,IAAI,KAAK,KAAK,QAAQ;YAAE,OAAM;QAC9B,IAAI,KAAK,KAAK,IAAI,IAAI,QAAQ,KAAK,EAAE;YAAE,OAAM;QAC7C,IAAI,KAAK,KAAK,EAAE,IAAI,QAAQ,KAAK,IAAI;YAAE,OAAM;QAE7C,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC,EAAE;YACpC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAA;SACnB;aAAM;YACL,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;SACtB;QAED,wDAAkC,CAAC,6BAAa,CAAC,OAAO,CAAC,CAAC,CAAA;KAC3D;AACH,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAAC,OAAgB,EAAE,SAAiB,EAC/D,QAAuB,EAAE,KAAoB,EAAE,SAAwB;IACvE;;;;;;;;;;;OAWG;IACH,IAAI,YAAK,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,SAAS,KAAK,MAAM,IAAI,SAAS,KAAK,IAAI,EAAE;QAC3E,IAAI,KAAK,KAAK,QAAQ;YAAE,OAAM;QAC9B,IAAI,KAAK,KAAK,IAAI,IAAI,QAAQ,KAAK,EAAE;YAAE,OAAM;QAC7C,IAAI,KAAK,KAAK,EAAE,IAAI,QAAQ,KAAK,IAAI;YAAE,OAAM;QAE7C,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC,EAAE;YACpC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAA;SACnB;aAAM;YACL,OAAO,CAAC,KAAK,GAAG,KAAK,CAAA;SACtB;QAED,IAAI,2CAAqB,CAAC,OAAO,CAAC,EAAE;YAClC,gDAA0B,CAAC,OAAO,CAAC,aAAqB,CAAC,CAAA;SAC1D;QAED,4CAAsB,CAAC,OAAO,CAAC,CAAA;KAChC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CAAC,OAAgB,EAAE,SAAiB,EAC5D,KAAoB,EAAE,SAAwB;IAC9C;;;;;OAKG;IACH,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,IAAI,EAAE;QAC5C,IAAI,CAAC,KAAK;YACR,OAAO,CAAC,iBAAiB,GAAG,SAAS,CAAA;;YAErC,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAA;KACpC;AACH,CAAC"}