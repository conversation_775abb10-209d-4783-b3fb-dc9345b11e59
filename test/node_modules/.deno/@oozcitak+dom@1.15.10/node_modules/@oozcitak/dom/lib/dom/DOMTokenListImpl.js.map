{"version": 3, "file": "DOMTokenListImpl.js", "sourceRoot": "", "sources": ["../../src/dom/DOMTokenListImpl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,qCAA+B;AAC/B,+CAAmE;AAEnE,yCAA8E;AAC9E,0CAIqB;AAErB;;GAEG;AACH;IAME;;;;;OAKG;IACH,0BAAoB,OAAgB,EAAE,SAAe;QAEnD;;;;;;;WAOG;QACH,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAA;QACvB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAA;QAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAA;QAE1B,IAAM,SAAS,GAAG,SAAS,CAAC,UAAU,CAAA;QACtC,IAAM,KAAK,GAAG,uCAA2B,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;QAE7D,8EAA8E;QAC9E,IAAM,OAAO,GAAG,IAAI,CAAA;QACpB,SAAS,cAAc,CAAC,OAAgB,EAAE,SAAiB,EACzD,QAAuB,EAAE,KAAoB,EAAE,SAAwB;YACvE;;;;;eAKG;YACH,IAAI,SAAS,KAAK,OAAO,CAAC,UAAU,CAAC,UAAU,IAAI,SAAS,KAAK,IAAI,EAAE;gBACrE,IAAI,CAAC,KAAK;oBACR,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;;oBAEzB,OAAO,CAAC,SAAS,GAAG,4BAAgB,CAAC,KAAK,CAAC,CAAA;aAC9C;QACH,CAAC;QACD,qEAAqE;QACrE,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAExD,IAAI,aAAG,CAAC,QAAQ,CAAC,KAAK,EAAE;YACtB,uCAA2B,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;SACpE;IACH,CAAC;IAGD,sBAAI,oCAAM;QADV,kBAAkB;aAClB;YACE;;;eAGG;YACH,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAA;QAC5B,CAAC;;;OAAA;IAED,kBAAkB;IAClB,+BAAI,GAAJ,UAAK,KAAa;;QAChB;;;;WAIG;QACH,IAAI,CAAC,GAAG,CAAC,CAAA;;YACT,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,SAAS,CAAA,gBAAA,4BAAE;gBAA/B,IAAM,KAAK,WAAA;gBACd,IAAI,CAAC,KAAK,KAAK;oBAAE,OAAO,KAAK,CAAA;gBAC7B,CAAC,EAAE,CAAA;aACJ;;;;;;;;;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,mCAAQ,GAAR,UAAS,KAAa;QACpB;;;WAGG;QACH,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;IAClC,CAAC;IAED,kBAAkB;IAClB,8BAAG,GAAH;;QAAI,gBAAmB;aAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;YAAnB,2BAAmB;;;YACrB;;;;;;;;eAQG;YACH,KAAoB,IAAA,WAAA,SAAA,MAAM,CAAA,8BAAA,kDAAE;gBAAvB,IAAM,KAAK,mBAAA;gBACd,IAAI,KAAK,KAAK,EAAE,EAAE;oBAChB,MAAM,IAAI,0BAAW,CAAC,4BAA4B,CAAC,CAAA;iBACpD;qBAAM,IAAI,iBAAc,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBACrD,MAAM,IAAI,oCAAqB,CAAC,kCAAkC,CAAC,CAAA;iBACpE;qBAAM;oBACL,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;iBAC1B;aACF;;;;;;;;;QACD,iCAAqB,CAAC,IAAI,CAAC,CAAA;IAC7B,CAAC;IAED,kBAAkB;IAClB,iCAAM,GAAN;;QAAO,gBAAmB;aAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;YAAnB,2BAAmB;;;YACxB;;;;;;;;eAQG;YACH,KAAoB,IAAA,WAAA,SAAA,MAAM,CAAA,8BAAA,kDAAE;gBAAvB,IAAM,KAAK,mBAAA;gBACd,IAAI,KAAK,KAAK,EAAE,EAAE;oBAChB,MAAM,IAAI,0BAAW,CAAC,+BAA+B,CAAC,CAAA;iBACvD;qBAAM,IAAI,iBAAc,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBACrD,MAAM,IAAI,oCAAqB,CAAC,kCAAkC,CAAC,CAAA;iBACpE;qBAAM;oBACL,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;iBAC7B;aACF;;;;;;;;;QACD,iCAAqB,CAAC,IAAI,CAAC,CAAA;IAC7B,CAAC;IAED,kBAAkB;IAClB,iCAAM,GAAN,UAAO,KAAa,EAAE,KAAsC;QAAtC,sBAAA,EAAA,iBAAsC;QAC1D;;;;WAIG;QACH,IAAI,KAAK,KAAK,EAAE,EAAE;YAChB,MAAM,IAAI,0BAAW,CAAC,+BAA+B,CAAC,CAAA;SACvD;aAAM,IAAI,iBAAc,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACrD,MAAM,IAAI,oCAAqB,CAAC,kCAAkC,CAAC,CAAA;SACpE;QAED;;WAEG;QACH,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC7B;;;;eAIG;YACH,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,KAAK,EAAE;gBAC1C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;gBAC5B,iCAAqB,CAAC,IAAI,CAAC,CAAA;gBAC3B,OAAO,KAAK,CAAA;aACb;YAED,OAAO,IAAI,CAAA;SACZ;QAED;;;WAGG;QACH,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;YACzC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;YACzB,iCAAqB,CAAC,IAAI,CAAC,CAAA;YAC3B,OAAO,IAAI,CAAA;SACZ;QAED;;WAEG;QACH,OAAO,KAAK,CAAA;IACd,CAAC;IAED,kBAAkB;IAClB,kCAAO,GAAP,UAAQ,KAAa,EAAE,QAAgB;QACrC;;;;;WAKG;QACH,IAAI,KAAK,KAAK,EAAE,IAAI,QAAQ,KAAK,EAAE,EAAE;YACnC,MAAM,IAAI,0BAAW,CAAC,gCAAgC,CAAC,CAAA;SACxD;aAAM,IAAI,iBAAc,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,iBAAc,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACtG,MAAM,IAAI,oCAAqB,CAAC,kCAAkC,CAAC,CAAA;SACpE;QAED;;;WAGG;QACH,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC;YAAE,OAAO,KAAK,CAAA;QAE5C;;;;WAIG;QACH,WAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;QACjD,iCAAqB,CAAC,IAAI,CAAC,CAAA;QAC3B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,mCAAQ,GAAR,UAAS,KAAa;QACpB;;;WAGG;QACH,OAAO,qCAAyB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IAC/C,CAAC;IAGD,sBAAI,mCAAK;QADT,kBAAkB;aAClB;YACE;;;eAGG;YACH,OAAO,oCAAwB,CAAC,IAAI,CAAC,CAAA;QACvC,CAAC;aACD,UAAU,KAAa;YACrB;;;;eAIG;YACH,uCAA2B,CAAC,IAAI,CAAC,QAAQ,EACvC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;QACtC,CAAC;;;OATA;IAWD;;OAEG;IACH,2BAAC,MAAM,CAAC,QAAQ,CAAC,GAAjB;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAA;QAE5C,OAAO;YACL,IAAI,EAAJ;gBACE,OAAO,EAAE,CAAC,IAAI,EAAE,CAAA;YAClB,CAAC;SACF,CAAA;IACH,CAAC;IAED;;;;;OAKG;IACI,wBAAO,GAAd,UAAe,OAAgB,EAAE,SAAe;QAC9C,OAAO,IAAI,gBAAgB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;IACjD,CAAC;IAEH,uBAAC;AAAD,CAAC,AApQD,IAoQC;AApQY,4CAAgB"}