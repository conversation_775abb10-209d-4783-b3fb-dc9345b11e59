{"version": 3, "file": "DocumentImpl.js", "sourceRoot": "", "sources": ["../../src/dom/DocumentImpl.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,qCAA+B;AAC/B,2CAKqB;AACrB,+CAEuB;AACvB,uCAAqC;AACrC,gCAA+B;AAC/B,uCAAqD;AACrD,yCAA6D;AAC7D,+DAA8D;AAC9D,0CASqB;AACrB,gEAA8D;AAE9D;;GAEG;AACH;IAAkC,gCAAQ;IAkCxC;;OAEG;IACH;QAAA,YACE,iBAAO,SACR;QApCD,eAAS,GAAG,IAAI,GAAG,EAAQ,CAAA;QAE3B,eAAS,GAAG;YACV,IAAI,EAAE,OAAO;YACb,MAAM,EAAE,CAAC,mBAAmB,EAAE,OAAO,EAAE,MAAM,CAAC;SAC/C,CAAA;QACD,kBAAY,GAAG,iBAAiB,CAAA;QAChC,UAAI,GAAG;YACL,MAAM,EAAE,OAAO;YACf,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,CAAC,OAAO,CAAC;YACf,KAAK,EAAE,IAAI;YACX,QAAQ,EAAE,IAAI;YACd,qBAAqB,EAAE,IAAI;YAC3B,aAAa,EAAE,IAAI;SACpB,CAAA;QACD,aAAO,GAAG,IAAI,CAAA;QACd,WAAK,GAAmB,KAAK,CAAA;QAC7B,WAAK,GAA8C,WAAW,CAAA;QAG9D,sBAAgB,GAAG,IAAI,CAAA;QACvB,oBAAc,GAAG,KAAK,CAAA;QAEtB,4BAAsB,GAAoB,IAAI,CAAA;;IAS9C,CAAC;IARD,sBAAI,uCAAa;aAAjB,cAAgC,OAAO,IAAI,CAAC,sBAAsB,IAAI,IAAI,CAAA,CAAC,CAAC;aAC5E,UAAkB,GAAa,IAAI,IAAI,CAAC,sBAAsB,GAAG,GAAG,CAAA,CAAC,CAAC;;;OADM;IAW5E,sBAAI,wCAAc;QADlB,kBAAkB;aAClB;YACE;;;eAGG;YACH,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,oCAAwB,CAAC,IAAI,CAAC,CAAC,CAAA;QACxF,CAAC;;;OAAA;IAGD,sBAAI,6BAAG;QADP,kBAAkB;aAClB;YACE;;;;eAIG;YACH,OAAO,4BAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACjC,CAAC;;;OAAA;IAGD,sBAAI,qCAAW;QADf,kBAAkB;aAClB,cAA4B,OAAO,IAAI,CAAC,GAAG,CAAA,CAAC,CAAC;;;OAAA;IAG7C,sBAAI,gCAAM;QADV,kBAAkB;aAClB;YACE,OAAO,MAAM,CAAA;QACf,CAAC;;;OAAA;IAGD,sBAAI,oCAAU;QADd,kBAAkB;aAClB;YACE;;;eAGG;YACH,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,YAAY,CAAA;QAC9D,CAAC;;;OAAA;IAGD,sBAAI,sCAAY;QADhB,kBAAkB;aAClB;YACE;;;;eAIG;YACH,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAA;QAC5B,CAAC;;;OAAA;IAGD,sBAAI,iCAAO;QADX,kBAAkB;aAClB,cAAwB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAA,CAAC,CAAC;;;OAAA;IAGpD,sBAAI,uCAAa;QADjB,kBAAkB;aAClB,cAA8B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAA,CAAC,CAAC;;;OAAA;IAG1D,sBAAI,qCAAW;QADf,kBAAkB;aAClB;YACE;;eAEG;YACH,OAAO,IAAI,CAAC,YAAY,CAAA;QAC1B,CAAC;;;OAAA;IAGD,sBAAI,iCAAO;QADX,kBAAkB;aAClB;;;gBACE;;;mBAGG;gBACH,KAAoB,IAAA,KAAA,SAAA,IAAI,CAAC,SAAS,CAAA,gBAAA,4BAAE;oBAA/B,IAAM,KAAK,WAAA;oBACd,IAAI,YAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC;wBACjC,OAAO,KAAK,CAAA;iBACf;;;;;;;;;YACD,OAAO,IAAI,CAAA;QACb,CAAC;;;OAAA;IAGD,sBAAI,yCAAe;QADnB,kBAAkB;aAClB;YACE;;eAEG;YACH,OAAO,IAAI,CAAC,gBAAgB,CAAA;QAC9B,CAAC;;;OAAA;IAED,kBAAkB;IAClB,2CAAoB,GAApB,UAAqB,aAAqB;QACxC;;;WAGG;QACH,OAAO,gDAAoC,CAAC,aAAa,EAAE,IAAI,CAAC,CAAA;IAClE,CAAC;IAED,kBAAkB;IAClB,6CAAsB,GAAtB,UAAuB,SAAwB,EAAE,SAAiB;QAChE;;;;WAIG;QACH,OAAO,4CAAgC,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;IACrE,CAAC;IAED,kBAAkB;IAClB,6CAAsB,GAAtB,UAAuB,UAAkB;QACvC;;;WAGG;QACH,OAAO,6CAAiC,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;IAC5D,CAAC;IAED,kBAAkB;IAClB,oCAAa,GAAb,UAAc,SAAiB,EAAE,OAAiC;QAChE;;;;;;;;;;;;;;WAcG;QAEH,IAAI,CAAC,sBAAU,CAAC,SAAS,CAAC;YACxB,MAAM,IAAI,oCAAqB,EAAE,CAAA;QAEnC,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM;YAAE,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,CAAA;QAE9D,IAAI,EAAE,GAAkB,IAAI,CAAA;QAC5B,IAAI,OAAO,KAAK,SAAS,EAAE;YACzB,IAAI,eAAQ,CAAC,OAAO,CAAC,EAAE;gBACrB,EAAE,GAAG,OAAO,CAAA;aACb;iBAAM;gBACL,EAAE,GAAG,OAAO,CAAC,EAAE,CAAA;aAChB;SACF;QAED,IAAM,SAAS,GACb,CAAC,IAAI,CAAC,KAAK,KAAK,MAAM,IAAI,IAAI,CAAC,YAAY,KAAK,uBAAuB,CAAC,CAAC,CAAC;YACxE,iBAAc,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAA;QAE9B,OAAO,mCAAuB,CAAC,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAC7D,EAAE,EAAE,IAAI,CAAC,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,sCAAe,GAAf,UAAgB,SAAwB,EAAE,aAAqB,EAC7D,OAAiC;QACjC;;;;WAIG;QACH,OAAO,4CAAgC,CAAC,IAAI,EAAE,SAAS,EACrD,aAAa,EAAE,OAAO,CAAC,CAAA;IAC3B,CAAC;IAED,kBAAkB;IAClB,6CAAsB,GAAtB;QACE;;;WAGG;QACH,OAAO,mCAAuB,CAAC,IAAI,CAAC,CAAA;IACtC,CAAC;IAED,kBAAkB;IAClB,qCAAc,GAAd,UAAe,IAAY;QACzB;;;WAGG;QACH,OAAO,uBAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAChC,CAAC;IAED,kBAAkB;IAClB,yCAAkB,GAAlB,UAAmB,IAAY;QAC7B;;;;;;;WAOG;QACH,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM;YACvB,MAAM,IAAI,gCAAiB,EAAE,CAAA;QAE/B,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC5B,MAAM,IAAI,oCAAqB,EAAE,CAAA;QAEnC,OAAO,+BAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACxC,CAAC;IAED,kBAAkB;IAClB,oCAAa,GAAb,UAAc,IAAY;QACxB;;;WAGG;QACH,OAAO,0BAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACnC,CAAC;IAED,kBAAkB;IAClB,kDAA2B,GAA3B,UAA4B,MAAc,EAAE,IAAY;QACtD;;;;;;;WAOG;QAEH,IAAI,CAAC,sBAAU,CAAC,MAAM,CAAC;YACrB,MAAM,IAAI,oCAAqB,EAAE,CAAA;QAEnC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3B,MAAM,IAAI,oCAAqB,EAAE,CAAA;QAEnC,OAAO,wCAA4B,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;IACzD,CAAC;IAED,kBAAkB;IAClB,iCAAU,GAAV,UAAW,IAAU,EAAE,IAAqB;QAArB,qBAAA,EAAA,YAAqB;QAC1C;;WAEG;QACH,IAAI,YAAK,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,YAAK,CAAC,YAAY,CAAC,IAAI,CAAC;YACxD,MAAM,IAAI,gCAAiB,EAAE,CAAA;QAE/B;;WAEG;QACH,OAAO,sBAAU,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IACrC,CAAC;IAED,kBAAkB;IAClB,gCAAS,GAAT,UAAU,IAAU;QAClB;;WAEG;QACH,IAAI,YAAK,CAAC,cAAc,CAAC,IAAI,CAAC;YAC5B,MAAM,IAAI,gCAAiB,EAAE,CAAA;QAE/B;;WAEG;QACH,IAAI,YAAK,CAAC,YAAY,CAAC,IAAI,CAAC;YAC1B,MAAM,IAAI,oCAAqB,EAAE,CAAA;QAEnC;;;WAGG;QACH,0BAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAC1B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,sCAAe,GAAf,UAAgB,SAAiB;QAC/B;;;;;;;WAOG;QACH,IAAI,CAAC,sBAAU,CAAC,SAAS,CAAC;YACxB,MAAM,IAAI,oCAAqB,EAAE,CAAA;QAEnC,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE;YACzB,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,CAAA;SACpC;QAED,IAAM,IAAI,GAAG,uBAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;QACzC,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,wCAAiB,GAAjB,UAAkB,SAAiB,EAAE,aAAqB;QAExD;;;;;WAKG;QACG,IAAA,kFACqB,EADpB,UAAE,EAAE,cAAM,EAAE,iBACQ,CAAA;QAE3B,IAAM,IAAI,GAAG,uBAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;QACzC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;QACpB,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAA;QAC9B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,kBAAkB;IAClB,kCAAW,GAAX,UAAY,cAAsB;QAChC,OAAO,mCAAuB,CAAC,cAAc,CAAC,CAAA;IAChD,CAAC;IAED,kBAAkB;IAClB,kCAAW,GAAX;QACE;;;WAGG;QACH,IAAM,KAAK,GAAG,wBAAY,EAAE,CAAA;QAC5B,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;QACxB,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;QACtB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,kBAAkB;IAClB,yCAAkB,GAAlB,UAAmB,IAAU,EAAE,UAAuC,EACpE,MAAiE;QADpC,2BAAA,EAAA,aAAyB,uBAAU,CAAC,GAAG;QACpE,uBAAA,EAAA,aAAiE;QAEjE;;;;;;;WAOG;QACH,IAAM,QAAQ,GAAG,+BAAmB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;QACtD,QAAQ,CAAC,WAAW,GAAG,UAAU,CAAA;QACjC,QAAQ,CAAC,mBAAmB,GAAG,2BAAe,CAAC,IAAI,CAAC,CAAA;QACpD,IAAI,iBAAU,CAAC,MAAM,CAAC,EAAE;YACtB,QAAQ,CAAC,OAAO,GAAG,6BAAiB,EAAE,CAAA;YACtC,QAAQ,CAAC,OAAO,CAAC,UAAU,GAAG,MAAM,CAAA;SACrC;aAAM;YACL,QAAQ,CAAC,OAAO,GAAG,MAAM,CAAA;SAC1B;QACD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,kBAAkB;IAClB,uCAAgB,GAAhB,UAAiB,IAAU,EAAE,UAAuC,EAClE,MAAiE;QADtC,2BAAA,EAAA,aAAyB,uBAAU,CAAC,GAAG;QAClE,uBAAA,EAAA,aAAiE;QACjE;;;;;;WAMG;QACH,IAAM,MAAM,GAAG,6BAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QAC5C,MAAM,CAAC,WAAW,GAAG,UAAU,CAAA;QAC/B,IAAI,iBAAU,CAAC,MAAM,CAAC,EAAE;YACtB,MAAM,CAAC,OAAO,GAAG,6BAAiB,EAAE,CAAA;YACpC,MAAM,CAAC,OAAO,CAAC,UAAU,GAAG,MAAM,CAAA;SACnC;aAAM;YACL,MAAM,CAAC,OAAO,GAAG,MAAM,CAAA;SACxB;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,oCAAa,GAAb,UAAc,KAAY;QACxB;;;;;WAKG;QACH,IAAI,KAAK,CAAC,KAAK,KAAK,MAAM,EAAE;YAC1B,OAAO,IAAI,CAAA;SACZ;aAAM;YACL,OAAO,aAAG,CAAC,MAAM,CAAA;SAClB;IACH,CAAC;IAED,8BAA8B;IAC9B,0BAA0B;IAC1B,qCAAc,GAAd,UAAe,SAAiB,IAAoB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAA,CAAC,CAAC;IAOrH,sBAAI,kCAAQ;QALZ,8BAA8B;QAC9B,cAAc;QAEd,oBAAoB;QACpB,0BAA0B;aAC1B,cAAiC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA,CAAC,CAAC;;;OAAA;IAExF,sBAAI,2CAAiB;QADrB,0BAA0B;aAC1B,cAA0C,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA,CAAC,CAAC;;;OAAA;IAEjG,sBAAI,0CAAgB;QADpB,0BAA0B;aAC1B,cAAyC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA,CAAC,CAAC;;;OAAA;IAEhG,sBAAI,2CAAiB;QADrB,0BAA0B;aAC1B,cAAkC,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA,CAAC,CAAC;;;OAAA;IACzF,0BAA0B;IAC1B,8BAAO,GAAP;QAAQ,eAA2B;aAA3B,UAA2B,EAA3B,qBAA2B,EAA3B,IAA2B;YAA3B,0BAA2B;;QAAU,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA;IAAC,CAAC;IACpG,0BAA0B;IAC1B,6BAAM,GAAN;QAAO,eAA2B;aAA3B,UAA2B,EAA3B,qBAA2B,EAA3B,IAA2B;YAA3B,0BAA2B;;QAAU,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA;IAAC,CAAC;IACnG,0BAA0B;IAC1B,oCAAa,GAAb,UAAc,SAAiB,IAAoB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA,CAAC,CAAC;IAC1G,0BAA0B;IAC1B,uCAAgB,GAAhB,UAAiB,SAAiB,IAAc,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA,CAAC,CAAC;IAEzG,mBAAC;AAAD,CAAC,AAjcD,CAAkC,mBAAQ,GAiczC;AAjcY,oCAAY;AAmczB;;GAEG;AACH,iCAAe,CAAC,YAAY,CAAC,SAAS,EAAE,WAAW,EAAE,qBAAQ,CAAC,QAAQ,CAAC,CAAA"}