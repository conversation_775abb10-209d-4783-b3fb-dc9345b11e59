[![npm (scoped)](https://img.shields.io/npm/v/@xml-tools/common.svg)](https://www.npmjs.com/package/@xml-tools/common)

# @xml-tools/common

Shared Utilities for XML-Tools

## Installation

With npm:

- `npm install @xml-tools/Common`

With Yarn

- `yarn add @xml-tools/Common`

## Usage

Please see the [TypeScript Definitions](./api.d.ts) for full API details.

Simply import/require the @xml-tools/common package and use the utilities
as defined in the APIs above.

```javascript
const xmlToolsCommon = require("@xml-tools/common");
xmlToolsCommon.findNextTextualToken(/*... */);
```

## Support

Please open [issues](https://github.com/SAP/xml-tols/issues) on github.

## Contributing

See [CONTRIBUTING.md](./CONTRIBUTING.md).
